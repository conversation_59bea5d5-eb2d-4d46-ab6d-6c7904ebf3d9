**************
WOLT DRIVE API
**************


*****************************************************
ECOMAIL - AUTOMATICKÉ EMAILY A BONUSY ZA NÁKUP Z NICH
*****************************************************

Pri pridávaní rôznych typov automatizačných mailov v Ecomaili je potrebné vložiť webhook na nasledujúcu URL adresu:

https://drinkcentrum.sk/mvc/Mailer/MailerContacts/processAutomaticEmailDataFromEcomail/{type}

type sa nahradí typom automatizovanej kampane, napr. reactivation, nameday, abandonedCart, ...

Tento endpoint slúži na spracovanie odoslaných dát a následný zápis príslušných údajov do profilu užívateľa
za účelom získania bonusových bodov ak sa nákupi do stanoveného času po obdržaní automatického mailu.

Ak by bolo potrebné daný typ automatizačných emailov obslúžiť osobitne/odlišne od iných typov tak to implementuj rovnako ako je to pre reactivation typ (viď nastavenia UserProfile.reactivationEmailOrderBonusPoints). Zahŕňa to nasledovné:
- Vytvorenie nastavení: 
    - Pre každý typ kampane musí byť definované nastavenie vo forme "UserProfile.{$type}EmailOrderBonusPoints" a "UserProfile.{$type}EmailOrderBonusPointsDays". 
    - Každá kampaň musí mať aspoň tieto dve nastavenia. Okrem týchto je možné pridať ešte "UserProfile.{$type}EmailOrderBonusPointsOrderTotal" ktoré určuje minimálnu sumu objednávky na získanie bodov. 
- Integrácia webhooku: 
    - Do ecomail automatizácie je potrebné pridať webhook na URL: https://drinkcentrum.sk/mvc/Mailer/MailerContacts/processAutomaticEmailDataFromEcomail/{type}
    - Parameter {type} určuje typ kampane a musí sa zhodovať s hodnotou použitou v názve nastavení.
- POZOR:
    - Názov nastavenia musí striktne dodržiavať formát "UserProfile.{$type}EmailOrderBonusPoints". Hodnota {$type} musí presne zodpovedať parametru type vo webhook URL. Inak sa nastavenie nenájde a použije sa predvolená hodnota z automaticEmailOrderBonusPoints.
- Zobrazenie hlášky v zhrnutí košíka:
    - Ak má byť v košíku zobrazená špecifická hláška k danej kampani, treba upraviť funkciu getPricesSummaryInfo v OrderConfirmationForm.tsx. 
    - V časti prechádzajúcej cez received_automatic_email_order_bonus_points, treba overiť, či pole obsahuje kľúč vo formáte automaticEmailOrder_{type}. Ak áno, do bonusPointsSources sa pridáva vlastná hláška (napr. ako pri automaticEmailOrder_reactivation).
    - POZOR: Pri sčítavaní bodov z ostatných kampaní v cykle nezapočítavaj kampane, pre ktoré už bola vytvorená vlastná správa – inak sa body spočítajú duplicitne.

**********************
PACKETA ODBERNÉ MIESTA
**********************
- Momentálne inicializácia mapy na užívateľovú polohu je zariadená cez geolocation
- Ak však užívateľ geolokáciu nemá povolenú v prehliadači, zobrazí sa mu mapa celej krajiny z ktorej môže miesto vybrať
- Toto by sa dalo zariadiť cez súradnice lat a ling v options widgetu, možnosť zadania adresy do options neexistuje
- Po zadaní súradníc je mapa síce nastavená na zadané súradnice ale je odzoomovaná na maximum tak, že tam nie je vidieť skoro ani krajinu
- Toto sa riešilo s Packeta podporou, ktorá poradila len vyplnenie poľa vendor kde by sa mapa focusla na špecifické odberné miesto čo ale nie je riešenie

*******************************
OVEROVANIE PLATIEB V TATRABANKE
*******************************
Overovanie platieb v TB (EshopOrders::updateTatrabankaPaymentsStatuses() a admin_checkTatrabankaPaymentStatus()) zlaháva na chýbajúcej "Authorization" hlavičke. Nača implementácia metódy PaymentMethodTatrabanka::checkTransactionStatus() je v zhode s aktuálnou dokumentáciou (https://www.tatrabanka.sk/tatrapay/TatraPay_technicka_prirucka_HMAC.pdf > strana 16) a z TB nám odpísali (<EMAIL>):

"
Mohli by ste nám prosím napísať, či sa Vám to prestalo vracať úplne alebo iba pri niektorých požiadavkách? A ideálne aj keby ste nám napísali, odkedy sa Vám to prestalo vracať.
Implementácia u nás v aplikácii sa nemenila a každá odpoveď z banky, ktorá nie je chybová, obsahuje hlavičku Authorization. Inak by sa odpoveď ani nevrátila.
Vyššie uvedené informácie od Vás by nám pomohli zistiť, či hlavičku neodstraňuje niečo po ceste.
"

Keďže nebol čas toto preverovať, tak s klientom (pani Ločajová) som sa dohodol že Tatrapay odstráním so zverejnených platobných metód (Card Pay sa nepoužíva už dlhšie) a pozastavil som CROn an spúšťanie kontroly (https://drinkcentrum.sk/mvc/Eshop/EshopOrders/updateTatrabankaPaymentsStatuses). Do TB som odpísal že to zatiaľ nejdeme riešiť.

Ak by to v budúcnosti chceli rozbehať tak je potrebné odladiť odpovede z TB (či okrem hlavičky chýba aj telo odpovede, ... nevznilko to náhoudou pri prenose na VS hosting?). Problém nie je v tom že CardPay sa už nepoužíva ("overovanie stavu platby pri službe TatraPay je možné aj v prípade, že nie je využívaná služba CardPay").

Je zvláštne, že hoci sa CardPay už davnejšie nepoužíva, tak stále sú objednávky, ktoré majú priradenú túto platobnú metódu. Nedeje sa to náhodou pri uložení objednávky v admine?

*******************************
JENOKROKOVÝ OBJEDNÁVKOVÝ PROCES
*******************************
vid app/modules/Eshop/js/react/EshopOrders/one-step-checkout/README.txt

********************
KAUFLAND MARKETPLACE
********************

Produkty sa prenášajú na Kaufland marketplace cez baselinker. Účty v Kauflande vytvorili v Dexfinity. Účet v baselinkeri má na starosti pán Ľubomír Petráš (<EMAIL>).

Prosím ešte si potvrďme, že sa rozprávame o tých istých XML feedoch.
Na import produktov z drinkcentrum.sk používate pre Kaufland SK nasledovné feedy:
- detaily produktov: https://drinkcentrum.sk/heureka_sk.xml
- skladová dostupnosť: https://drinkcentrum.sk/heureka_availability_sk.xml

Pre Kaufland CZ sa používajú tieto súbory:
- detaily produktov: https://drinkcentrum.sk/heureka_cs.xml
- skladová dostupnosť: https://drinkcentrum.sk/heureka_availability_cs.xml


**********************************
POTVRDZOVANIE A RUŠENIE OBJEDNÁVOK
**********************************
Potvrdzujú a rušia sa len objednávky v stave "Nepotvrdená" (enum_unconfirmed_order). Pre tieto sa generuje informačná vsuvka :unconfirmedOrderInfo: v maili novej objednávky.
Ak bude potrebné zmeniť podmienky pre stav "Nepotvredená" (alebo to vypnúť), tak stačí upraviť vyhodnotenie pridelenia tohto stavu v metóde EshopOrder::getNewOrderStatus().


*******************
ČÍSELNÍK "ZLOŽENIA"
*******************
Ak by bolo potrebné niekedy pridať prelinkovania na jednotlivé zloženia (EshopIngredient) tak viď revíziu de2d3ed3fb53 (tu sa to pridávalo) alebo revíziu 05336dd28875 (tu sa to odstránilo)

**************************
FILTER V DETAILE PRODUKTOV
**************************
- Na pridanie nového filtra viď revíziu 974d4e767f39 (Ciselniky - zapracovanie Znacky, Krajiny, Regionu a Balenia do filtra v zozname produktov kategorie).
- Na odstránenie filtra viď revíziu 190ed7a7caa0 (Odstranenie (zakomentovanie) Kategorie a Balenia vo filtri v zozname produktov kategorie)


************************
PRENOS OBJEDNÁVOK DO DPD
************************

Otázky a <NAME_EMAIL>:
--------------------------------------
- na posielanie B2C objednávok vrámci SK je najlepšie použiť produkt DPD Home (id 9) s tým, že ak má objednávka platbu dobierkou, tak sa do dat zásielky pridá sekcia "cod" s patričnými údajmi.
    >> Ano potvrdzujeme pre taketo dorucenie je ideálne DPD Home lebo klient je 2x notifikovaný
- na posielanie B2B objednávok vrámci SK je najlepšie použiť produkt DPD Classic (id 1) - DPD Home pre tento typ zásielok asi nie je dostupný?
    >> pre B2B klientov možete posielat aj DPD Home ale je na zvaženie ci nejaká spolocnost potrebuje notifikacie o dorucení, ak pri B2B DPD Classic vyplnite notifikacnu cast v API requeste príjemca bude o dorucení informovaný tiez.
- ktorý produkt je vhodné použiť na posielanie B2C objednávok do zahraničia (CZ, HU)?
    >> Je to na vás ale zvyčajne sa používa B2B pre zahranicne zásielky, viete tam ale vytvárat aj B2C.
- v params.shipment.reference je zrejme možné / vhodné uviesť číslo objednávky...? Alebo niekde inde?
    >> možete sem zadávat číslo objednávky ale tato informácia bude len v response a nebude na štítku.
- aký je rozdiel medzi params.shipment.note a params.shipment.addressRecipient.note?
    >> params.shipment.note sa zobrazuje na prepravnom štítku ak si to nastavíte pri tlaci štítka v UI. params.shipment.addressRecipient.note sa nezobrazuje na prepravnom štítku
- je potrebné nastavovať params.shipment.pickup ak sú dohodnuté nejaké pravidelné zvozy?
    >> ano je to nutne aj ked je dohodnutý pravidelný zvoz (mimo systém)
- id zrejme nie je potrebné vypĺňať ...? Kedy by to malo zmýsel?
    >> má to zmysel ak by ste si niekde u seba ukladali requesty a chcete ich rozoznat, ak nie stací default zadávat napríklad „null“
- params.shipment.parcels.parcel - nakoľko sú rozmery a váhy balíkov dôležité? Je ok pokiaľ sa všetko nastaví na "1"?
    >> ak posielate gro balíkov do 3 kg tak možete default nastavit číslo 3 alebo 1 ako píšete ak gro balíkov posielate nad 3kg tak použite číslo 4
- je možné niekde zadať hodnotu objednávky kvôli poisteniu? Pre prípad že hodnota tovaru presiahne základné poistenie.
    >> Hodnotu objednávky môžete zadať do nejakej referencie ale nebude to mať vplyv na naše RMA oddelenie.
- je API dokumentácia (shipperAPI-doc-2.6-v20212911.pdf) dostupná aj online?
    >> Momentálne špecifikácia nie je dostupná online lebo ešte stale obcas v nej dochádza k zmenám, zatial mate aktuálnu verziu
- aké jazyky môžeme nastavovať pre notifikácie?
    >> Čo sa týka jazykov notifikácii tak vždy používajte SK. Je pravda že niekedy to tak fungovalo že bolo potrebné zadávať jazyk príjemcu ale to už nie je platne lebo sa zmenil notifikačný proces krajín. Notifikácie posiela krajina príjemcu a ta potrebuje len číslo v správnom medzinárodnom formáte a email.


*********************************************
ROZHRANIE NA ROBENIE PREKLADOV PRIAMO NA WEBE
*********************************************
Je to urobené v rýchlej verzii kde sa HU preklady prepisujú priamo na produktoch.
Pre konečné nasadenie je potrebné oddeliť tieto texty do osobitnej tabuľky v ktorej budú
preklady nielen pre produkty ale pre ľubovolné texty (owner_mode, owner_id), dorobiť nejaké stavy 
(nový preklad, rozpracovaný, hotový) a prideľovanie prekladov jednotlivým prekladateľom.
Viď tiket #479 > "Pripravíme rozhranie na preklady pre prekladateľov."


****
MALL
****

product => run_eshop_products

ID – product.code
STAGE
    - draft – pri prvom nahrávaní
    - live – pri opakovanom nahrávaní
CATEGORY_ID -  run_eshop_product_categories.mall_id
    - do  run_eshop_product_categories mall_id
    - zapracovať do administrácie
BRAND_ID -  product.run_eshop_manufacturers_id.mall_id
    - do  run_eshop_manufacturers pridať mall_id
    - zapracovať do administrácie
TITLE - product.name
    - nesmie obsahovať názov značky
    - pred zapísaním zmazať reťazec značky z názvu
    - max dĺžka 200 znakov
SHORTDESC - product.seo_description
    - nesmie obsahovať HTML tagy
    - max dĺžka 300 znakov
LONGDESC - product.description
    - môže obsahovať HTML tagy
    - max dĺžka 13000 znakov
PRIORITY - vždy 1
PACKAGE_SIZE - smallbox/bigbox
    - smallbox musí spĺňať 3 podmienky:
        - súčet všetkých 3 strán je do 175cm
        - najdlhšia strana je do 100cm
        - váha balenia je do 20kg
BARCODE - product.ean
PRICE - cena vrátane DPH
VAT - DPH celým číslom (%), napr. 21
RRP - odporúčaná maloobchodná cena, pre CZ len celé čísla
PARAM : {
    name: identifikátor (napr. VOLUME_OF_ALCOHOL),
    value: hodnota parametru
}
MEDIA - obrázky produktu (max. 20)
{
    URL: max veľkosť 2MB / 2000x2000px, max 200 znakov
    MAIN: true/false podľa toho či sa jedná o hlavný obrázok produktu
}
PROMOTION: {
    PRICE: akciová cena product.discount_price / discount_rate
    FROM: ve formátu 2002-05-30T19:00:00 product.discount_from
    TO: ve formátu 2002-05-30T19:00:00 product.discount_to
}
DIMENSIONS: {
    WEIGHT: v kg product.weight
    WIDTH: šírka v cm product.width
    HEIGHT: výška v cm product.height
    LENGTH: dĺžka v cm product.length
}
DELIVERY_DELAY - počet pracovných dní do doručenia produktu (0 - beria sa z nastavení dopravy na mall portály)
FREE_DELIVERY - true/false


****************
EXTJS LIKE ADMIN
****************
Pri vytváraní nového modulu je možné obísť obťiaže spojené s jeho nasadzovaním 
v ExtJS admine - viď administračné metódy v kontroleri EshopSpecialOffers.php

Ak by si túto možnosť chcel preniesť do iného fajnworkového projektu s ExtJS adminom,
tak to urob podľa revízie 6ab05f9967c7


****************
ŠPECIALNE PONUKY
****************

Preniesli sa z vydavatel.sk s nasledovnými zmenami:
- pre enum EshopSpecialOffer.apply_by sa pridala hodnota 'promoted_products_cart_amount_threshold' a zapracovalo sa to do logiky
- pridalo sa pole EshopSpecialOffer.cart_amount_threshold
- možnosti EshopSpecialOffer.apply_by > 'cart_price_threshold' a 'promoted_products_cart_price_threshold' sa na drinku skryli (viď app/modules/Eshop/views/EshopSpecialOffers/admin_form.php > // hide following options on the project)
- app/modules/Eshop/views/EshopSpecialOffers/admin_form.php > :cartPrice: sa skrylo
- app/modules/Eshop/views/EshopSpecialOffers/admin_form.php > inputy k zobrazeniu v bočnom menu sú zakomentované
- v EshopCart::applySpecialOffers() sa produktom s aplikovanými ponukami nastavuje aj 'apply_by' aby to bolo explicitnejšie/čiteteľnejšie
- plus mnoho iných zmien súvisiacich so zapracovaním nového typu ŠP. V zásade však platí že existujúca funkcionlita vydavatel.sk sa zachovala plus sa rozšírila/upratela/sčitateľnila
-! Ak by sa to prenášalo niekde ďalej tak verzia z drinkcentra je najvhodnejšia

Detailny popis viď na vydavatel.sk: vydavatel/misc/docs/implementationDetails.md > ŠPECIALNE PONUKY
Na drinkcentre sa pridali "ŠP uplatnené na množstvo promovaných produktov v košíku". Tieto sa vlastne správajú tak isto ako "ŠP uplatnené na cenu promovaných produktov v košíku" (ŠPC) len sa nameisto celkovej ceny vyhodnocuje celkové množstvo.

Momentálny stav:
- môže byť aplikovaná iba jedna ŠP
- na mnohých miestach sa s ráta s tým že potencionálne by mohl mať jeden produkt viacero ŠP ale v konečnom dosledku sa validácie nastavili tak že produkt môže byť zaradený len v jednej aktívnej ŠP

Skratky:
- P, Ps - promoted product(s)
- D, Ds - discounted product(s)
- SO, SOs - special offer(s)
- SOPPC - special offer applied by promoted products in cart
- SOCP - special offer applied by cart price
- SOPPCP - special offer applied by promoted products cart price
- SOPPCA - special offer applied by promoted products cart amount

Automatické pridávanie produktov:
---------------------------------
Pri automatickom pridávaní D produktov do košíka je pravidlo na vyhodnocovanie výhodnosti SO trochu otázne: 

    {promotedProductActualPrice} - {discountedProductSpecialSavings}

Pokiaľ je špeciálna zľavnená cena D zadaná pevne (0€, 5€, ...) je to ok. 
Ale ak je špeciálna zľava percentuálna tak by to trebalo prehodnotiť. Napr pre ŠP 50% zľava:

    P: 20€
    D1: 10€ => 5€ špeciálna cena
    D2: 100€ => 50€ špeciálna cena
    
Ak produktu D1 a D2 sám pridám do košíka tak pravidlo je OK - zo sumy ktorú by som mal zaplatiť my vyberie tú väčšiu úsporu. 
Ak sa však má D1 alebo D2 pridať automaticky tak nie je možné rozhodnúť ktoré je pre klienta výhodnejšie. 

Výsledné pravidlo na automatické pridávanie je také že buď je len 1 D produkt (a špecialna zľava možeby t aj absolútna aj percentuálna) alebo ak je viac D produktov tak špeciálna zľava môže byť len absolútna

********************************************************************
UPOZORNENIE O PLATENI SPOTREBNEJ DANE PRI OBJEDNÁVKACH DO ZAHRANIČIA
********************************************************************
- pri krajinách kde má DC otvorenú daňovú pobočku to stačí zobrazovať len pre objednávky nad 10 litrov

Toto upozornenie sa pomocou EshopCart::setVolumeLimitAppMessage() potencionálne zobrazuje (vyhľadaj ->setVolumeLimitAppMessage):
- pri pridaní produktu do košíka
- pri úprave košíka
- pri zadaní adresy doručenia v objednávkovom procese

Na uvedených miestach sa upozornenie (za predpokladu, že limit sa prekročil) nezobrazuje vždy ale 
len tak, aby to nebolo otravné pre užívateľa. Logiku zobrazenia viď v EshopCart::setVolumeLimitAppMessage()


***********************************
NÁZVY KATEGÓRIÍ AKO "DRUH ALKOHOLU"
***********************************

Ak je zaškrtnuté pole EshopProductCategory.is_kind_of_alcohol tak prvá najdená kategória 
(v poradí od prvej najvnorenejšej až po poslednú hlavnú kategóriu) sa zobrazí v detaile 
produktu ako "Druh alkoholu".

V projekte je už zapracovaná aj podobná funkcionalita ktorá prídavala názvy top kategórií
k názvom produktu. Od 13.12.2019 je to vypnuté (len DB updatom, viď app/updates/2019/2019-12-13_durik.sql)
Táto funkcionalita používa pole EshopProductCategory.normalize_product_name.

Hoci som najprv myslel, že EshopProductCategory.is_kind_of_alcohol a EshopProductCategory.normalize_product_name
je viacmenej niečo podobné/rovnaké a chcel som to zjednotiť tak som to nakoniec nechal oddelené, 
lebo predsa to len má nejaké rozdielnosti (normalize_product_name sa vyhodnocuje len pre hlavné kategórie)
Ak by však niekedy vznikla vôľa, tak to je možné zjednotiť.

***********
SOFT DELETE
***********

Pri začisťovaní duplicitných produktov v DB bola prenesená do drinkcentra "soft-delete" logika.
Nie úplne celá. Len tie najdôležitejšie výskyty v triede Model, aby duplicitné produkty bolo možné "soft deletnúť".

V modeloch User, MailerCampaign a MailerContact je soft-delete funkcionalita vypnutá tým, že property $deletedField 
je nastavená na NULL. Detaily viď v phpDoce $deletedField v daných modeloch.

*****************
PRÍBUZNÉ PRODUKTY
*****************
V admin formulári sa danému produktu priraďujú príbuzné produkty.
Zapracovanie ohľadom administrácie je v EshopProducts::admin_getSelectorInterface(), EshopProduct::findAll() a ::saveAll().

Príbuzné produkty k danému produktu sa zobrazujú v detaile produktu na frontende,
ale len také, ktoré sú publikované a dostupné (t.j. nie sú vypredané).

******************
VARIANTY PRODUKTOV
******************
V admin formulári sa danému produktu priraďujú varianty produktov.
Zapracovanie ohľadom administrácie je v EshopProducts::admin_getSelectorInterface(), EshopProduct::findAll() a ::saveAll().

Slúži to na spajanie variantov toho istého produktu pre heuréku (https://sluzby.heureka.sk/napoveda/xml-feed/#ITEMGROUP_ID).
Vyhľadaj variants_group_id v app/modules/Eshop/models/EshopExport.php

Zapracované je to aj v EshopProduct::getDetails() ale len formálne, nikde sa to nepoužíva.

***************************************************************************
CRAWLOVANIE KONKURENČNÝCH WEBOV - EshopProducts::importCompetitorProducts()
***************************************************************************
Na párovanie sa používa EAN. T.j. všetky competitor produkty (cp) musia mať EAN.
Ale nie všetky cp musia byť napárované na existujúce produkty (p). 
Ukladanie aj nespárovaných cp umožňuje efektívnekší fast processing (načítanie ceny produktu len zo zoznamu produktov, nie je nutné ísť do detailu produktu kvôli eanu).
Momentálne sa výtvárajú nespárované aj pre konkurenciu ktorú čítame z heureka XML - tu však fast processing nemá zmysel. Ak by si chcel pre týchto konkurentov vytvárať len spárované cp tak uprav podmienku pri vytváraní cp na nasledovnú:

                elseif (
                    // !!! each created competitor product must have an EAN to allow 
                    // pairing with eshop products
                    !empty($record['ean'])
                    // allow to create unpaired competitor products only to web readers
                    // (for sake of future fast processing)
                    && (
                        $Reader instanceof EshopWebReader
                        || !empty($existingProductIds[$record['ean']])
                    )
                ) {

Klientovi by sa dalo navrhnúť ešte nasledovné:
----------------------------------------------
- tam kde nie je EAN tak párovať podľa názvu - ak sa všetky slová názvu dajú navzájom spárovať (z oboch strán)
- export produktov ktoré konkurencia má a drinkcentrum nie (alebo nie sú spárované)
- export produktov ktoré má drinkcentrum a nie konkurencia (alebo nie sú spárované)
- mohli by mať možnosť párovať nespárované produkty ručne

**********
LUIGISBOX
**********
Údaje o produktoch si LB ťahá z Heuréka feedu.

Na prenášanie B2B cien pre sklady (1) a gastro (2) sa používajú dva pridané stlpce
PRICE_B2B_1 a PRICE_B2B_2 - oba obsahujú B2B ceny bez DPH. Plus sa body tagu pripájajú
css triedy 'b2b-user-1' / 'b2b-user-2' aby LB vedel kedy je prihlásený B2B user a aké
ceny mu zobrazovať.


*******************
NAHRÁVANIE OBRÁZKOV
*******************
Kým fungujú weby .sk a .cz na rozličných FTP serveroch, je nutné nahrávať obrázky len v slovenskej administrácií. Po prechode drinkcentrum.sk na nový dizajn sa toto obmedzenie odstráni.

*********************************
KONVERTOVANIE CIEN EUR NA CZK/HUF
*********************************
Konverzný kurz v nastavení Eshop.czk/hufConversionRate sa udržiava rovnaký pre všetky jazyky
(bez ohľadu na to v ktorom sa upraví) pomocou Setting::synchronizeTranslatedValues() v Settings::admin_update().


CZK/HUF ceny produktov sa prepočítavajú pomocou EshopProduct::synchronizePricesInAlternativeCurrencies() pri:
    - aktualizácii nastavení v Settings::admin_update()
    - aktualizácii produktu v EshopProduct::saveAll()
    - importe údajov produktov v EshopImport::importStock()
Prepočítavajú sa vždy € na Kč/Ft (bez ohľadu na to v ktorom sa synchronizácia spustí). 
Ak sa príznak EshopProduct._synchronize_prices_cs/hu nastaví na 0 tak je možné nastaviť osobitné ceny v € a v Kč/Ft.
V tomto prípade je však potrebné myslieť na to že discount_price_1/2 v € sa pravidelne aktualizujú cez 
EshopImport::importStock(). CZK/HUF verzie discount_price_1/2 v prípade _synchronize_prices_cs/hu = 0 ostanú pevné.

************************************************************
VÝBER PRODUKTOV S DOSTUPNOSŤOU "MOMENTALNE NIE JE NA SKLADE"
************************************************************
Spusti .../_debug/exportProductsAtSupplierStock


***************************************
SPOJENIE UZIVATELOV A KONTAKTOV MAILERA
***************************************
Viď phpDoc k modelu Core.User


***********************************************************
REFAKTORING DOSTUPNOSTI PRODUKTOV A ICH SYNCHRONIZACIE S IS
***********************************************************

Východzí stav, ktorý sa tento refaktoring snaží opraviť/vylepšiť, je nasledovný:
- pri synchronizačnom importe produktov z IS sa nastavujú nasledovné príznaky produktom:
    - `active` = 0 pre produkty ktoré nie sú vo feede (v drinkcentre povedia že "nie sú v cenníku"). Pôvodne to požadovali preto, aby sa produkty, ktoré nemajú skladom, na webe deaktivovali a nebolo ich možné objednať (vznikali ťahanice s klientami). Potom to ale zmenili a rozhodli sa že predsa len chcú predávať aj produkty ktoré nie sú skladom - veď zohnať St Nicola vodku nie je problém aj keď momentálne nie je na sklade a nejaké tie ťahanice už vydržia. Tým pádom vo feede sú skoro všetky produkty (vypadnú odtiaľ len veľmi vynimočne, keď sa výrobca rozhodne zrušiť výrobu nejakej značky) a nijak sa nerieši, že predsa len niektoré (najmä zahraničné) je ťažko zohnať.
    - `intenal` pre novovytvorené produkty (podľa kódu), aby sa tieto nezobrazovali na fronte (viď podmienka v EshopProduct::getPublishedConditions()), keďže im chýba obrázok a cena.
- horeuvedené viedlo k tomuto:
    - príznak `active` sa nedal používať na dočasné deaktivovanie produktu, lebo sa pri každej synchronizácii prepísal na TRUE
    - preto začali pridávať do kódu produktu na webe 'x' alebo 'w' aby sa tento nesynchronizoval. No na pozadí zakaždým vznikol duplicitný nevyplnený (internal) produkt ktorý sa nezobrazoval zas preto lebo je nevyplnený (takže vznikol naktívny produkt s kódom x123 a nevyplnený produkt s kódom 123).
    - preto namiesto pridávania 'x'/'w' začali na deaktivovanie produktov používať prízna `internal` - t.j. momentálne nie všetky internal sú naozaj internal... niektoré sú len "deaktivované"


A preto sa to prerobí takto (momentálne je už toto platné):
-----------------------------------------------------------

Produkty budú na frontende v nasledovných troch stavoch:
- "Na sklade":
    - funguje tak ako doteraz
- "Momentálne nie je na sklade":
    - je určený pre produkty s krátkou dodaciu lehotou (EshopProduct.long_delivery_time = 0)
    - funguje tak ako doteraz
- "Nie je na sklade":
    - je určený pre produkty s dlhou dodaciu lehotou (EshopProduct.long_delivery_time = 1).
    - príznak "Dlhá doba dodania" sa nastavuje ručne a je možné odfiltrovať podľa neho aj v admin zozname produktov.
    - tento príznak sa zachováva aj potom ako nabehne produktu nejaký skladový stav pretože keď sa skladová zásoba minie tak produkt bude zas potrebné objednať a zas bude len s dlhou dobou dodania
    - nebude možné pridať produkt do košíka
    - bude tam watchdog ktorý pošle email keď bude produkt buď znovu skladom alebo sa odškrtne príznak "Dlhá doba dodania"
- produkty so stavom "Vypredané" na drinkcentre neexistujú. Za týmto účelom sa používa soft-delete pri synchronizácii s IS (viď vyššie)

Zo strany p. Zboňáka sa pri synchronizácii stavov dostupnotí produktov z IS nič nezmení.
Z nášej strany nastanú pri synchronizácii s IS nasledovné zmeny:
    - namiesto poľa 'active' sa bude na "skrývanie" produktov používať soft-delete. Takéto produkty budú skryté nie len na frontende ale aj v backende, zobrazujú sa len v detaile objednávky (frontend/backend/export do IS). Soft-deletnú sa všetký produkty, ktoré nie sú prítomné v exporte z IS (v súbore STOCK_...). Ak sa produkt opäť objaví v exporte z IS, tak sa obnoví (zruší sa soft-delete príznak) a do mailu príde upozornenie o obnove tohoto produktu (aby sa skontrolovalo či stále platia udaje produktu). Na základe požiadavky klienta sa aktualizuje aj pole created a obnovené produkty sa v admin indexe potom zobrazujú medzi prvými rovnako ako novovytvorené. Raz za čas sa z IS odstránia karty produktov, ktoré sú už nadobro vypredané (aby tam v zoznamoch nezavadzali a aby mali karty na vytváranie nových produktov). Tieto karty sa môžu opäť zrecyklovať na nahratie nejakého iného produktu. Viď tu nižšie popis ako je recyklovanie ošetrené.
    - pole 'active' sa začne používať podľa svojho intuitívneho významu na rúčné skrývanie produktov na frontende. Je potrebné ho urobiť prekladané, aby bolo možné osobitne vypínať pre .sk a pre .cz
    - pole 'internal' bude fungovať tak ako sa volá, t.j. bude označovať nevyplnené produkty. Úloha skrývania/deaktivovania sa prenesie na pole 'active'

Karta v IS sa recykluje len vtedy keď bola z IS vymazaná. V žiadnom prípade nie vtedy keď bola len vyradená z cenníku! Takéto vymazané karty sa môžu z rôznych dôvodov recyklovať na nahratie nových produktov do systému. Po recyklovaní karty na nový produkt dôjde k nezhode medzi obnoveným produktom na webe (recyklovaná karta sa určite zaradí do cenníka a objaví sa v exporte z IS pre web) a produktom na karte. Adminovi príde síce informatívny mail, že produkt bol obnovený a admin môže upraviť údaje produktu podľa karty no to zas rozbije údaje starých objednávok zoznam ktorých je dostupný v užívateľskom profile. Riešenie tejto situácie je nasledovné:
- p. Zboňák pri mazaní kariet pošle zoznam kódov zmazaných kariet
- tento zoznam sa zadá v admine do nastroja na odpojenie produktov od kódov zmazaných kariet, t.j. soft-deleted produktom s uvedenými kódmi sa tieto kódy prepíšu na také aké z IS nemôžu prísť. Napríklad kód 123 sa prepíše na x123 - treba však zabezpečiť unikátnosť lebo karta 123 sa môže recyklovať viackrát. Pre produkty, ktoré nie sú soft-deleted sa zobrazí chyba, že tento produkt nie je možné odpojiť od kódu karty keďže je karta stále v používaní.

Skript /mvc/Eshop/EshopProducts/admin_indexSuspiciousProducts slúži na zobrazenie podozrivých produktov. Pôvodne skript skúžil na mazanie (soft-delete) týchto produktov, no porefaktoringu (viď rev af6838a57206) to stratilo význam - soft-delete sa používa pri synchronizácii s IS a ak produkt nie je v importe tak soft-deleted, inak nie alebo je obnovený. Akékoľvek iné dočasné mazanie produktov je prepísané týmto (tak ako predtým príznak active).


Ak by sa raz chceli zobrazovať aj vypredané produkty (momentálne soft-deleted), tak je potrebné si uvedomiť že v exporte z IS sa produkty nenachádzajú z dvoch rôzných príčin:
    - vypadnú z cenníka (toto sa deje veľmi zriedkavo)
    - ide o podradenú / združenú kartu, t.j. je to ten istý produkt no z nejakého dôvodu má vaiacero EANov a vytvorili sa mu preto viacere karty. Na web sa posiela len jedna z nich, ostatné sú nastavené ako podradené karty. Skladový stav na hlavnej karte je súčet skladových stavov s jej podkartami.

Ako vypredané sa môžu zobrazovať len tie prvé (čo vypadli z cenníka). Tie druhé (podradené karty) musia ostať skryté.
V takomto prípade by sa musel zmeniť aj export z IS - buď prvé alebo druhé by v ňom už museli byť prítomné
a nejak označené.



**************************************************************
IMPEMENTACIA INTERNYCH PRODUKTOV - PRODUKTY WEBU = PRODUKTY IS
**************************************************************
Produkty z IS importujem s príznakom "Nevyplnený" (internal = 1) a na frontend sa nezobrazujú
pokiaľ nie je zadaná cena a príznak "Nevyplnený" je zaškrtnutý.

Nové "Nevyplnené" produkty importujem (vytváram):
    - len ak majú obe B2B ceny a názov
    - so stavom "dostupný"
    - základnú cenu im nastavujem na 0
    
Pri aktualizácii produktov (všetkých) aktualizujem skladové stavy, EANy a B2B cieny (IS nevie nič o webových cenách)
"Nevyplnené" produkty som tiež vynechal z
- exportu do cenových porovnávačov (heureka, ...)
- exportu pre rumovú banku
- exportu pre Google Adwords

V Admin > Eshop > Produkty je možné
- v zozname odfiltrovať "Nevyplnené" produkty
- vo formulári zmeniť príznak "Nevyplnený". Po odškrtnutí a pridaní ceny sa produkt začne zobrazovať na verejnom webe.

Produkty, ktoré sa v importnom súbore "STOCK_" nachádzajú sú nastavené ako "Aktívne".
Produkty, ktoré sa v importnom súbore "STOCK_" nenachádzajú sú nastavené ako "Neaktívne".

Čo sa týka zlučovania položiek, tak v importnom súbore "STOCK_" sú uvedené vždy len hlavné položky a na ních sa akumuluje aj skladový stav.
Podradené položky v importnom súbore nie su (tie sa tým pádom nastavia na webe ako "Neaktívne").


************************************************
Eshop - objednať je možné len produkty na sklade
************************************************
Užívateľ môže umiestniť do košíka len produkty, ktoré sú aktuálne skladom.
Ak produkt bol pridaný do košíka vo väčšom množstve ako je dostupné na sklade, tak pri
zobrazení košíka sa množstvá produktov upravia na max možné a užívateľ je na to upozornený.

Čo sa týka tlačidla "Vložiť do košíka"  - jeho zobrazenie v zoznamoch a detailoch produktov som zatiaľ nemenil (t.j. zobrazuje sa aj pre produkty ktoré nie sú skladom).
Po kliknutí na "Vložiť do košíka" sa však v prípade, že produkt nie je skladom, zobrazí užívateľovi upozornenie, že môže kontaktovať obchodné oddelenie.

V náväznosti na tieto zmeny treba zvážiť:
-----------------------------------------
- či by predsa len niektoré notoricky dostupné produkty nemali byť zakúpiteľné aj v prípade, že nie sú skladom (bežná vodka, rum, gin, ...)
- či v hromadnej objednávke (rozhraní pre obchodníkov) má význam zobrazovanie produktov, ktoré nie sú skladom, keď ich nie je možné zakúpiť


******************************
IMPLEMENTACIA NAVRHNUTYCH CIEN
******************************
Ide o súčasť obchodníckeho rozhrania "Hromadná objednávka".
S p. Bindasom a p. Zboňákom sme sa dohodli na nasledovnom spôsobe fungovania:
    - do IS sa odošle objednávka tak ako doteraz no navrhnuté ceny sa použijú namiesto aktuálnych
    - klientovi sa pošle email "Novej požiadavky" (nie "Objednávky") so zoznamom produktov v ktorom budú navrhnuté ceny použité namiesto aktuálnych (nič nebude vysvietené)
    - do drinkcentra sa pošle ten istý email ako klientovi ale navrhnuté ceny tam budú vysvietené (? a mohli by byť aj preškrtnuté pôvodné)
    - ak sa objednávka vybavý tak p. Zboňak odošle mail s ostrou fakturou (zavolaj mu či to tak funguje)

Čo sa týka konkrétneho zapracovania v kóde tak je to na každom mieste ošetrené osobitne cez if-y.
Najľahšie sa nájdu všetky zapracovania vyhľadaním '_suggested'. Tie if-y sú pomerne zdĺhavé lebo
okrem navrhovanej ceny sa rieši ešte aj s/bez DPH pre b2c/b2b klientov.


*****************
DOPRAVA ZDARMA DO
*****************
- dá sa nastaviť v Admin > Nastavenia > Eshop > Hlavné > Doprava > Zadarmo do dátumu
- osobitne je potrebné upraviť dátum (a prípadne cenu) na drinkcentrum.cz:
- ak sa nezadá žiadny dátum, doprava zdarma pri nákupe nad stanovenú sumu je aktívna trvale

******************************************
VERZIE ZĽAVNENÝCH CIEN PRE SKLADY A GASTRO
******************************************
- sklady majú discount_price_1
- gastro majú discount_price_1
- presné zadelenie skupín viď v MailerGroup::getGastroPricelistGroupIds()/getSkladyPricelistGroupIds() a ::getDiscountPriceCategory()


********************************
OBRÁZOK V HLAVIČKE MAILOV ESHOPU
********************************
.../img/emails/email_header_original.jpg
https://www.peakpx.com/en/hd-wallpaper-desktop-nrmdn

***************
FONT V LOGOTYPE
***************
https://www.dafont.com/arista-pro.font?text=DRINKCENTRUM.sk&psize=l

******
BONUSY
******
- Fungujú tak že užívateľ si z každho nákupu pripočíta bonusové body na základe ceny zakúpených produktov (podelenej nastavením EshopOrder.bonusPointPrice) do UserProfile.bonus_points. Keď táto súma prekročí nastavenie Eshop.EshopOrder.applicableBonusPoints a uplatní si zľavu v objednávkovom procese, tak sa z objednávky odpočíta bonus podľa nastavenia Eshop.EshopOrder.bonusDiscount (vždy celé násobky) a z UserProfile.bonus_points sa odpočítajú použité body.
- Cena produktov sa započíta do UserProfile.bonus_points až pozaplatení danej objednávky. Ak sa objednávka na ktorej sa uplatnil bonus (a vynulovalo sa UserProfile.bonus_points) zruší, tak užívateľovi sa do UserProfile.bonus_points naspäť pripočíta Eshop.EshopOrder.applicableBonusPoints (v násobkoch podľa uplatnenej zľavy).
- Ak by cena po odpočítaní bonudu bola záporná, napr. cena objednávky je 8 eur a odpočíta sa bonus 10 eur, tak sa odpočíta len bonus 8 eur a zvyšné body v hodnote 2 eur ostanú klientovi na ďalšie zbieranie.
- Pri využití zľavy sa získané body za objednávku pripisujú s malou nepresnosťou - do získaných bodov sa pripíše pôvodná bodová hodnota objednávky bez ohľadu na aplikovaný bonus. Toto sa deje s vedomým klienta - konzultovali sme to s ním.
- Funkcionalita bonusov sa síce dá vypnúť (keď sa nastavia zodpovedné nastavenia na prázdne hodnoty - aj toto by trebalo preveriť) ale nedá sa skryť (zobrazenie linkov a informačných textov je napevno)