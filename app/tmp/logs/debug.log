>> 2025-03-12 14:05:39 199.21/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-12 14:05:44 161.83/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-12 14:08:41 250.48/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-12 14:09:35 262.47/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-12 14:09:38 235.37/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-12 14:09:57 262.24/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-12 14:10:58 280.28/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-12 14:11:27 280.22/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-12 14:11:38 276.83/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-12 14:12:18 200.39/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-12 14:12:33 330.18/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-12 14:12:52 296.52/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-12 14:15:43 284.97/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-12 14:16:16 167.59/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-12 14:17:17 262.65/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-12 14:20:59 252.49/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-12 14:22:16 117.46/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-12 14:22:28 255.61/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-12 14:26:39 5850.72/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-12 14:30:41 131.85/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-12 14:31:19 172.21/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-17 21:43:17 21880.26/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-17 21:43:50 326.21/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-17 21:44:55 222.64/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-17 21:45:36 214.39/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-17 21:51:47 269.21/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-17 21:52:09 266.94/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-17 21:53:57 286.54/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-17 21:54:40 251.14/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-17 21:54:49 260.19/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-17 21:58:32 259.71/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-17 22:32:53 4182.88/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-17 22:33:10 291.5/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-17 22:33:35 148/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-17 22:34:16 121.77/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-17 22:36:54 134.45/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-17 22:37:05 125.45/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-17 22:37:14 160.15/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-17 22:37:19 174.71/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-17 22:38:22 172.69/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-17 22:42:31 130.28/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-17 22:42:39 197.28/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-17 22:42:52 163.51/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-17 22:42:56 156.38/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-17 22:43:17 208.13/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-17 22:44:48 130.34/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-17 22:45:50 133.69/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-17 22:45:53 158.69/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-17 22:50:03 141.99/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-17 22:51:23 163.96/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-17 22:52:39 138.73/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-17 22:52:45 214.38/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-17 22:53:11 139.66/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-17 22:53:37 159.7/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-17 22:54:07 137.86/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-17 22:56:42 209.67/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-17 22:57:18 198.41/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-17 22:58:04 147.62/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-17 22:59:03 131.9/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-17 23:00:08 125.25/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-17 23:01:38 138.93/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-17 23:09:23 273.36/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-17 23:09:30 268.96/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-17 23:09:44 283.38/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-17 23:10:05 297.39/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-17 23:11:01 3383.79/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-17 23:12:09 325.89/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-17 23:12:50 285.82/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-17 23:13:36 279.94/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-17 23:15:07 300.39/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-17 23:16:25 271.64/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-17 23:17:30 297.34/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-17 23:19:07 139.46/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-17 23:19:26 120.03/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-17 23:19:29 154.17/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-17 23:21:09 123.71/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-17 23:22:27 137.61/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-17 23:23:05 198.2/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-17 23:24:26 370.44/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-17 23:25:18 260.14/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-17 23:26:27 310.7/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-17 23:26:50 275.18/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-17 23:36:10 346.52/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-17 23:39:26 287.4/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-17 23:47:28 269.34/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-18 00:04:09 362.03/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-18 00:04:35 321.73/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-18 00:05:03 327.17/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-18 00:07:50 275.41/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-18 00:08:30 299.11/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-18 00:09:14 252.83/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-18 00:11:45 316.16/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-18 00:15:31 328.33/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-18 00:19:01 279.59/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-18 00:20:37 245.57/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-18 00:27:30 193.96/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-18 00:28:18 265.18/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-18 00:29:47 268.88/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-18 00:31:49 392.53/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-18 00:32:21 286.32/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-18 00:33:16 359.18/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-18 00:33:52 124.92/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-18 00:34:15 152.65/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-18 00:34:45 163.77/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-18 00:35:03 118.53/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-18 00:38:18 153.99/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-18 00:43:17 122.3/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-18 00:49:11 116.47/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-18 00:49:22 122.17/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-18 00:49:39 123.73/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-18 00:49:59 127.38/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-18 00:50:35 146.16/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-18 00:51:15 119.68/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-18 00:51:33 207.02/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-18 00:51:53 129.83/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-18 00:52:22 180.04/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-18 00:52:48 161.15/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-18 00:56:33 129.54/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-18 00:56:55 130.05/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-18 01:06:48 170.74/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-18 01:07:09 170.54/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-18 01:13:25 160.67/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-18 01:13:43 144.14/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-18 01:14:00 133.39/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-18 01:14:34 119.69/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-18 01:14:38 144.07/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-18 01:14:46 116.19/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-18 01:14:55 120.24/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-18 01:16:17 317.57/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-18 01:16:49 249.26/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-18 01:17:02 269.57/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-18 01:17:44 302.54/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-18 01:21:15 253.98/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-18 01:27:57 194.83/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-18 01:30:33 141.33/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-03-18 01:31:05 169.36/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-05-11 19:57:40 44951.21/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3.1 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-05-11 19:59:27 65199.13/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3.1 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-05-11 20:00:51 2164.41/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3.1 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-05-11 20:02:43 116.29/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3.1 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-05-11 20:05:21 116.66/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3.1 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-05-11 20:05:53 112.22/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3.1 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-05-11 20:08:15 50169.79/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3.1 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-06-24 09:51:13 129.93/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-06-24 09:51:13 239.98/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-06-24 09:52:11 129.05/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-06-24 09:52:12 284.87/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-07-24 17:10:39 213.08/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-07-24 17:29:47 124.99/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-07-24 18:39:34 149.86/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-07-24 18:41:26 71879.97/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-07-24 18:43:08 3171.82/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-07-24 18:44:21 142.51/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-07-24 18:47:15 126.47/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-07-24 18:57:53 118.89/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-07-24 19:24:06 268.67/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-07-24 19:24:11 140.52/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-07-24 19:27:11 131.47/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-07-24 19:27:56 146.67/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-07-24 19:40:03 145.77/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-07-24 20:26:59 228.25/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-07-24 20:27:05 135.88/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-07-24 20:28:05 182.65/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-07-24 20:28:05 158.64/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-07-25 10:39:27 338.49/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/26.0 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-08-19 10:00:03 392.76/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/26.0 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-08-21 09:22:10 127.73/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/26.0 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-08-21 09:28:07 115.66/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/26.0 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-08-21 09:36:30 145.31/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/26.0 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-08-21 10:34:28 138.82/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/26.0 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-08-21 10:57:07 139.09/600000ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/26.0 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
>> 2025-08-21 11:01:21 306.87/9.2233720368548E+18ms 192.168.65.1 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/26.0 Safari/605.1.15 (GET /mvc/Eshop/EshopOrders/api_getOneStepCheckoutData)
 - [[DEBUG: E_NOTICE: Undefined variable: availableCartPrice] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995) (FB cannot be used as headers has been sent already. Console is used instead.) (Html debug output cannot be used in this layout. Log is used instead.)] /var/www/html/app/modules/Eshop/models/EshopCartSpecialOffersResolver.php (line 995): 
    E_NOTICE: Undefined variable: availableCartPrice
