<?php
/**
 * @class EshopOrders
 * A simple application controller extension
 */
class EshopOrders extends Controller {

    protected $Model;

    public function __construct(){
        parent::__construct();
        $this->loadModel('EshopOrder'); 
        $this->loadModel('EshopGA4DataLayer');
        $this->Model = new EshopOrder();
    }
    
    /**
     * view
     * Retrieves rows from database.
     */
    public function admin_index($merchantPid = null, $b2b = false) {
        // normalize merchant pid
        if (!$merchantPid) {
            $merchantPid = null;
        }
        $Res = new ExtResponse();
        
        // check if user has rights for required orders
        $userGroupAdminLang = App::getUser('Group.admin_lang');
        if (
            $userGroupAdminLang === 'cs'
            && $merchantPid !== 'drinkcentrum.cz'
            ||
            $userGroupAdminLang === 'hu'
            && $merchantPid !== 'drinkcentrum.hu'
        ) {
            $Res->errors = array(__e(__FILE__, 'Invalid request'));
            $Res->success = false;
            return $Res->toJson();
        }

        $EshopOrder = new EshopOrder();
        $options = $EshopOrder->getAdminIndexFindOptions($merchantPid, $b2b);
        $options['fields'] = array_merge($options['fields'], array(
            'EshopOrder.id',
            'EshopOrder.number',
            'EshopOrder.token',
            'EshopOrder.specific',
            'EshopOrder.unapplied_volume_limits',
            'EshopOrder.run_users_id',
            'EshopOrder.status',
            'EshopOrder.payment_status',
            'EshopOrder.business_type',
            'EshopOrder.order_price_taxless',
            'EshopOrder.order_tax',
            'EshopOrder.order_price_actual_taxless',
            'EshopOrder.order_tax_actual',
            'EshopOrder.order_price_suggested_taxless',
            'EshopOrder.order_tax_suggested',
            'EshopOrder.order_price_to_pay',
            'EshopOrder.bonus_discount',
            'EshopOrder.products_price_taxless',
            'EshopOrder.products_tax',
            'EshopOrder.products_tax_actual',
            'EshopOrder.products_price_suggested_taxless',
            'EshopOrder.products_tax_suggested',
            'EshopOrder.shipment_price_taxless',
            'EshopOrder.shipment_tax',
            'EshopOrder.shipment_price_actual_taxless',
            'EshopOrder.shipment_tax_actual',
            'EshopOrder.payment_price_taxless',
            'EshopOrder.payment_tax',
            'EshopOrder.payment_price_actual_taxless',
            'EshopOrder.payment_tax_actual',
            'EshopOrder.recycling_fee_taxless',
            'EshopOrder.recycling_fee_tax',
            'EshopOrder.run_eshop_shipment_methods_id',
            'EshopOrder.shipment_method_name',
            'EshopOrder.run_payment_methods_id',
            'EshopOrder.payment_method_name',
            'EshopOrder.fullname',
            'EshopOrder.street',
            'EshopOrder.city',
            'EshopOrder.zip',
            'EshopOrder.country',
            'EshopOrder.email',
            'EshopOrder.phone',
            'EshopOrder.company_name',
            'EshopOrder.company_street',
            'EshopOrder.company_city',
            'EshopOrder.company_country',
            'EshopOrder.company_zip',
            'EshopOrder.company_id_number',
            'EshopOrder.company_tax_number',
            'EshopOrder.company_vat_tax_number',
            'EshopOrder.delivery_fullname',
            'EshopOrder.delivery_street',
            'EshopOrder.delivery_city',
            'EshopOrder.delivery_country',
            'EshopOrder.delivery_zip',
            'EshopOrder.delivery_phone',
            'EshopOrder.phone',
            'EshopOrder.email',
            'EshopOrder.is_fraud',
            'EshopOrder.warnings',
            'EshopOrder.comment',
            'EshopOrder.dedication',
            'EshopOrder.merchant_pid',
            'EshopOrder.shipment_ordered',
            'EshopOrder.created',
            'EshopOrder.modified',       
            'EshopOrder.exported',       
        ));
        $Res->data = $EshopOrder->find($options);
        
        // prepare virtual field shipment_price for specific orders (e.g. delivery to abroad)
        $pricesAreTaxed = $this->getSetting('pricesAreTaxed');
        foreach ($Res->data as &$item) {
            $item['specific_shipment_price'] = $item['shipment_price_actual_taxless'];
            if (
                $pricesAreTaxed
                && $item['specific_shipment_price'] !== null
            ) {
                $item['specific_shipment_price'] += $item['shipment_tax_actual'];
            }
        }
        
        // find count
        $Res->total = $EshopOrder->findCount($options);
        
        $Res->success = true;
        return $Res->toJson();
    }
    
    public function admin_getIndexSummary($merchantPid = null, $b2b = false) {
        // normalize merchant pid
        if (!$merchantPid) {
            $merchantPid = null;
        }
        $Res = new ExtResponse();
        
        // check if user has rights for required orders
        $userGroupAdminLang = App::getUser('Group.admin_lang');
        if (
            $userGroupAdminLang === 'cs'
            && $merchantPid !== 'drinkcentrum.cz'
            ||
            $userGroupAdminLang === 'hu'
            && $merchantPid !== 'drinkcentrum.hu'
        ) {
            $Res->errors = array(__e(__FILE__, 'Invalid request'));
            $Res->success = false;
            return $Res->toJson();
        }

        $EshopOrder = new EshopOrder();
        $options = $EshopOrder->getAdminIndexFindOptions($merchantPid, $b2b);
        $ordersCountFieldSql = 'COUNT(*) AS orders_count';
        $ordersTotalFieldSql = 'SUM(EshopOrder.order_price_actual_taxless + EshopOrder.order_tax_actual) AS orders_total';
        $shipmentsTotalFieldSql = 'SUM(EshopOrder.shipment_price_actual_taxless + EshopOrder.shipment_tax_actual) AS shipments_total';
        
        $options['fields'] = array_merge($options['fields'], array(
            $ordersCountFieldSql,
            $ordersTotalFieldSql,
            $shipmentsTotalFieldSql,
        ));
        $options['literals']['fields'] = array_merge($options['literals']['fields'], array(
            $ordersCountFieldSql,
            $ordersTotalFieldSql,
            $shipmentsTotalFieldSql,
        ));
        $options['limit'] = null;
        $options['offset'] = null;
        $result = $EshopOrder->findFirst($options);
        
        // prepare summary custom param
        $merchantLang = $EshopOrder->getLangByMerchantPid($merchantPid);
        $Res->data = array(
            'summary' => __a(
                __FILE__, 
                'Počet objednávok: <b>%s</b>. Suma celkovej ceny: <b>%s</b>. Suma poštovného: <b>%s</b>',
                $result['orders_count'],
                Eshop::formatPrice($result['orders_total'], array('lang' => $merchantLang)),
                Eshop::formatPrice($result['shipments_total'], array('lang' => $merchantLang))
            ),
        );
        
        return $Res->toJson();
    }
    
    public function admin_create() {
        $Res = new ExtResponse();

        $EshopOrder = new EshopOrder();
        if ($EshopOrder->save($this->data, array('create' => true))) {
            
            $Res->success = true;
            $Res->message = array(
                'text' => __a(__FILE__, 'Creation of eshopOrder has success'),
                'type' => 'info'
            );
            $this->data['id'] = $EshopOrder->getId();
            $this->data['created'] = date('Y-m-d H:i:s');
            $this->data['modified'] = date('Y-m-d H:i:s');
            $Res->data = $this->data;
        }
        else {
            $Res->success = false;
            $Res->errors = $EshopOrder->getErrors();
            $Res->message = array(
                'text' => __a(__FILE__, 'Creation of eshopOrder has failed'),
                'type' => 'error',
                'errors' => $EshopOrder->getErrors()
            );
        }
        return $Res->toJson();
    }  
    
    public function admin_update($withProducts = false) {
        $Res = new ExtResponse();
        $this->data = Utility::inflateArray($this->data);
        unset($this->data['modified']);
        $EshopOrder = new EshopOrder();
        $EshopShipmentMethod = $this->loadModel('EshopShipmentMethod', true);
        $PaymentMethod = App::loadModel('Payment', 'PaymentMethod', true);
        $oldOrder = $EshopOrder->findFirstBy('id', $this->data['id'], array(
            'fields' => array(
                'specific',
                'status',
                'merchant_pid',
                'is_fraud',
                'recycling_fee_taxless',
                'recycling_fee_tax',
                'bonus_discount',
                'run_users_id',
            )
        ));

        App::loadModel('Core', 'User');
        $User = new User();
        
        $originalLang = App::setI18n($EshopOrder->getLangByMerchantPid($oldOrder['merchant_pid']));
        
        $calculateOrder = false;
        if (isset($this->data['shipping'])) {
            $calculateOrder = true;
            $shipValues = explode(';', $this->data['shipping']);
            $this->data['run_eshop_shipment_methods_id'] = $shipValues[0];
            $this->data['run_payment_methods_id'] = $shipValues[1];
            unset($this->data['shipping']);
            
            // get data of shipment and payment
            $shipment = $EshopShipmentMethod->findFirstBy('id', $this->data['run_eshop_shipment_methods_id']);
            $payment = $PaymentMethod->findFirstBy('id', $this->data['run_payment_methods_id']);   
            
            if (
                !$oldOrder['specific'] 
                || !isset($this->data['specific_shipment_price']) 
            ) {
                $this->data['shipment_method_name'] = $shipment['name'];
            }
            $this->data['payment_method_name'] = $payment['name'];
        }
        $explicitShipmentPrice = false;
        if (
            $oldOrder['specific'] 
            && isset($this->data['specific_shipment_price']) 
        ) {
            $calculateOrder = true;
            $explicitShipmentPrice = true;
            $shipment['price'] = $this->data['specific_shipment_price'];
        }
        
        $productPrices = array(
            'products_price_taxless' => 0,
            'products_tax' => 0,
            'products_price_actual_taxless' => 0,
            'products_tax_actual' => 0,
            'products_price_to_pay' => 0,
        );
        $EshopOrderProduct = $this->loadModel('EshopOrderProduct', true);
        DB::startTransaction('EshopOrders_admin_update');
        if ($withProducts) {
            $EshopOrderProduct->delete(array(
                'conditions' => array(
                    'run_eshop_orders_id' => $this->data['id']
                )
            ));
        }
        
        try {
            // save order items
            if (isset($this->data['products'])) {
                foreach ($this->data['products'] as $item) {
                    if (
                    !$EshopOrderProduct->save(array(
                        'run_eshop_orders_id' => $this->data['id'],
                        'run_eshop_products_id' => $item['id'],
                        'amount' => $item['amount'],
                        'price_taxless' => $item['price_taxless'],
                        'tax' => $item['tax'],
                        'price_actual_taxless' => $item['price_actual_taxless'],
                        'tax_actual' => $item['tax_actual'],
                        'tax_rate' => $item['tax_rate'],
                        'static_attributes' => $item['static_attributes'],
                        'dynamic_attributes' => $item['dynamic_attributes'],
                    ))
                    ) {
                        App::setI18n($originalLang);
                        DB::rollbackTransaction('EshopOrders_admin_update');
                        $Res->message = array(
                            'text' => __a(__FILE__, 'Update of eshopOrderProduct has failed'),
                            'type' => 'error',
                            'errors' => $EshopOrderProduct->getErrors()
                        );
                        return $Res->toJson();
                    }
                    $productPrices['products_price_taxless'] += $item['price_taxless'] * $item['amount'];
                    $productPrices['products_tax'] += $item['tax'] * $item['amount'];
                    $productPrices['products_price_actual_taxless'] += $item['price_actual_taxless'] * $item['amount'];
                    $productPrices['products_tax_actual'] += $item['tax_actual'] * $item['amount'];
                }
                
                $taxedOrder = true;
                if (!empty($oldOrder['run_users_id'])) {
                    $taxedOrder = !$User->isB2b($oldOrder['run_users_id']);
                }
                $productPrices['products_price_to_pay'] = $productPrices['products_price_actual_taxless'];
                if ($taxedOrder) {
                    $productPrices['products_price_to_pay'] += $productPrices['products_tax_actual'];
                }
                if (
                    array_key_exists('bonus_discount', $oldOrder)
                    && (float)$oldOrder['bonus_discount']
                ) {
                    $productPrices['products_price_to_pay'] -= (float)$oldOrder['bonus_discount'];
                }
            }

            // calculate order prices
            if ($calculateOrder &&
                isset($this->data['run_eshop_shipment_methods_id']) && 
                isset($this->data['run_payment_methods_id'])) {
                // calculate shipment prices
                $shipmentPrices = $EshopShipmentMethod->getPrices($shipment, array(
                    // if shipment price is set explicitly (usng virtual field specific_shipment_price)
                    // then do not apply implicit changes
                    'productsPrices' => $explicitShipmentPrice ? null : $productPrices,
                ));
                // calculate payment prices
                $paymentPrices = $PaymentMethod->getPrices($payment, array(
                    'productsPrices' => $productPrices,
                ));
                
                $this->data['order_price_taxless'] = 
                        $productPrices['products_price_taxless'] 
                        + $shipmentPrices['shipment_price_taxless'] 
                        + $paymentPrices['payment_price_taxless'] 
                        + $oldOrder['recycling_fee_taxless'];
                $this->data['order_tax'] = 
                        $productPrices['products_tax'] 
                        + $shipmentPrices['shipment_tax'] 
                        + $paymentPrices['payment_tax']
                        + $oldOrder['recycling_fee_tax'];
                $this->data['order_price_actual_taxless'] = 
                        $productPrices['products_price_actual_taxless'] 
                        + $shipmentPrices['shipment_price_actual_taxless'] 
                        + $paymentPrices['payment_price_actual_taxless']
                        + $oldOrder['recycling_fee_taxless'];
                $this->data['order_tax_actual'] = 
                        $productPrices['products_tax_actual'] 
                        + $shipmentPrices['shipment_tax_actual'] 
                        + $paymentPrices['payment_tax_actual']
                        + $oldOrder['recycling_fee_tax'];
                // @synchro with EshopOrder::getPrices()
                
                $taxedOrder = true;
                if (!empty($oldOrder['run_users_id'])) {
                    $taxedOrder = !$User->isB2b($oldOrder['run_users_id']);
                }
                $this->data['order_price_to_pay'] = $productPrices['products_price_to_pay'];
                $this->data['order_price_to_pay'] += $shipmentPrices['shipment_price_actual_taxless']
                    + $paymentPrices['payment_price_actual_taxless'];            
                if ($taxedOrder) {
                    $this->data['order_price_to_pay'] += $shipmentPrices['shipment_tax_actual']
                        + $paymentPrices['payment_tax_actual'];
                }
                $this->data['products_price_taxless'] = $productPrices['products_price_taxless'];
                $this->data['products_tax'] = $productPrices['products_tax'];
                $this->data['products_price_actual_taxless'] = $productPrices['products_price_actual_taxless'];
                $this->data['products_tax_actual'] = $productPrices['products_tax_actual'];
                $this->data['products_price_to_pay'] = $productPrices['products_price_to_pay'];
                $this->data['shipment_price_taxless'] = $shipmentPrices['shipment_price_taxless'];
                $this->data['shipment_tax'] = $shipmentPrices['shipment_tax'];
                $this->data['shipment_price_actual_taxless'] = $shipmentPrices['shipment_price_actual_taxless'];
                $this->data['shipment_tax_actual'] = $shipmentPrices['shipment_tax_actual'];
                $this->data['payment_price_taxless'] = $paymentPrices['payment_price_taxless'];
                $this->data['payment_tax'] = $paymentPrices['payment_tax'];
                $this->data['payment_price_actual_taxless'] = $paymentPrices['payment_price_actual_taxless'];
                $this->data['payment_tax_actual'] = $paymentPrices['payment_tax_actual'];
            }

            if ($EshopOrder->save($this->data)) {

                $Res->data = array(
                    'modified' => date('Y-m-d H:i:s')
                );
                // check if status has been changed
                if (
                    !empty($oldOrder) 
                    && isset($this->data['status']) 
                    && $oldOrder['status'] != $this->data['status']
                ) {
                    // send email about changed status
                    if (
                        $this->data['status'] != 'enum_new_order'
                        // do not send changed-status-email (canceled) to fraud orders 
                        // to not let them know that they are revealed
                        && !$oldOrder['is_fraud']
                        && (
                            $oldOrder['merchant_pid'] !== 'rumovabanka'
                            && (
                                ($msgBody = $this->getSetting(
                                    'EshopOrder.msgBodyStatusChange.' . $this->data['status']
                                ))
                                || ($msgBody = $this->getSetting(
                                    'EshopOrder.msgBodyStatusChange'
                                ))
                            )
                            ||
                            $oldOrder['merchant_pid'] === 'rumovabanka'    
                            && (
                                ($msgBody = $this->getSetting(
                                    'EshopOrder.msgBodyStatusChangeRB.' . $this->data['status']
                                ))
                                || ($msgBody = $this->getSetting(
                                    'EshopOrder.msgBodyStatusChangeRB'
                                ))
                            )
                        )
                    ) {
                        $msgSubject = $this->getSetting(
                            'EshopOrder.msgSubjectStatusChange.' . $this->data['status']
                        );
                        if (empty($msgSubject)) {
                            $msgSubject = $this->getSetting(
                                'EshopOrder.msgSubjectStatusChange'
                            );
                        }
                        $hasSuggestedPrices = false;
                        $inserts = $EshopOrder->getInserts($this->data['id'], $hasSuggestedPrices);
                        if (!empty($inserts[':userEmail:'])) {
                            if ($EshopOrder->sendEmail(
                                $this->data['id'],
                                $msgBody, 
                                $inserts[':userEmail:'], 
                                array(
                                    'subject' => $msgSubject,
                                    'from' => $EshopOrder->getSenderEmail($oldOrder['merchant_pid']),
                                    'cc' => $EshopOrder->getCopyEmail($oldOrder['merchant_pid']),
                                    'inserts' => $inserts,
                                    'embedImages' => true,
                                )
                            )) {
                                App::setI18n($originalLang);
                                $Res->success = true;
                                $Res->message = array(
                                    'text' => __a(__FILE__, 'Update of eshopOrder has success and notification email has been sent'),
                                    'type' => 'info'
                                );
                                DB::commitTransaction('EshopOrders_admin_update');
                                return $Res->toJson();
                            } else {
                                App::setI18n($originalLang);
                                $Res->success = false;
                                $Res->message = array(
                                    'text' => __a(__FILE__, 'Notification email sending failed. Order was not updated'),
                                    'type' => 'info'
                                );
                                DB::rollbackTransaction('EshopOrders_admin_update');
                                return $Res->toJson();
                            }
                        }
                    }
                    // export order csv
                    if ($this->data['status'] === 'enum_new_order') {
                        $Export = $this->loadModel('EshopExport', true);
                        $Export->exportOrderCsv($this->data['id']);
                    }
                }
                App::setI18n($originalLang);
                $Res->success = true;
                $Res->message = array(
                    'text' => __a(__FILE__, 'Update of eshopOrder has success'),
                    'type' => 'info'
                );
            } else {
                App::setI18n($originalLang);
                DB::rollbackTransaction('EshopOrders_admin_update');
                $Res->message = array(
                    'text' => __a(__FILE__, 'Update of eshopOrder has failed'),
                    'type' => 'error',
                    'errors' => $EshopOrder->getErrors()
                );
                
                return $Res->toJson();
            } 
        } catch (Throwable $e) {
            App::setI18n($originalLang);
            DB::rollbackTransaction('EshopOrders_admin_update');
            throw new Exception($e->getMessage());
        }
        DB::commitTransaction('EshopOrders_admin_update');
        
        return $Res->toJson();
    }  

    public function admin_updateMallOrder() {
        $Res = new ExtResponse();
        $Res->success = true;
        $EshopOrder = new EshopOrder();

        $id = Sanitize::value($_POST['orderId']);

        if (!$id || !Validate::intNumber($id)) {
            $Res->success = false;
            $Res->message = array(
                'text' => __a(__FILE__, 'Wrong identifikator of order id'),
                'type' => 'error'
            );
            return $Res->toJson();
        }
        if($EshopOrder->updateMallOrder($id)){

        }
    }

    public function admin_delete() {
        $Res = new ExtResponse();
        $EshopOrder = new EshopOrder();
        $EshopOrderProduct = $this->loadModel('EshopOrderProduct', true);
        if (
            // put the deleted order to canceled status at first 
            // to correctly treat received and applied bonus points
            $EshopOrder->save(
                array(
                    'id' => $this->data['id'],
                    'status' => 'enum_canceled_order'
                ),
                array(
                    'normalize' => false,
                    'validate' => false,
                )
            )
            && $EshopOrder->deleteBy('id', $this->data['id'])
            && $EshopOrderProduct->deleteBy('run_eshop_orders_id', $this->data['id'])
        ) {
            $Res->success = true;
            $Res->message = array(
                'text' => __a(__FILE__, 'Deletion of eshopOrder has success'),
                'type' => 'info'
            );
            $Res->data = array();
            App::log(
                'EshopOrders_admin_delete', 
                __a(
                    __FILE__,
                    'Eshop order id %s has been deleted by user %s',
                    $this->data['id'],
                    App::getUser('username')
                )
            );
            return $Res->toJson();
        }
        $Res->success = false;
        $Res->errors = $EshopOrder->getErrors();
        $Res->message = array(
            'text' => __a(__FILE__, 'Deletion of eshopOrder has failed'),
            'type' => 'error',
            'errors' => $EshopOrder->getErrors()
        );

        return $Res->toJson();
    } 
    
    public function admin_load() {
        $Res = new ExtResponse();
        $Res->success = true;
        $EshopOrder = new EshopOrder();

        $id = @$_POST['orderId'];
        if (empty($id) || !is_numeric($id)) {
            $Res->success = false;
            $Res->message = array(
                'text' => __a(__FILE__, 'Wrong identifikator of order id'),
                'type' => 'error'
            );
            return $Res->toJson();
        }

        $Res->data = $EshopOrder->findFirst(array(
            'conditions' => array(
                'id' => $id
            ),
        ));
        return $Res->toJson();
    }
    
    public function admin_loadWithItems() {
        $Res = new ExtResponse();
        $Res->success = true;
        $EshopOrder = new EshopOrder();
        
        $id = @$_POST['orderId'];
        if (empty($id) || !is_numeric($id)) {
            $Res->success = false;
            $Res->message = array(
                'text' => __a(__FILE__, 'Wrong identifikator of order id'),
                'type' => 'error'
            );
            return $Res->toJson();
        }

        $Res->data = $EshopOrder->findFirst(array(
            'conditions' => array(
                'id' => $id
            ),
        ));
        $EshopProduct = $this->loadModel('EshopProduct', true);
        $Res->data['items'] = $EshopProduct->find(array(
            'fields' => array(
                'EshopProduct.id',
                'EshopProduct.name',
                'EshopProduct.internal_name',
                'EshopOrderProduct.price_taxless',
                'EshopOrderProduct.tax',
                'EshopOrderProduct.price_actual_taxless',
                'EshopOrderProduct.tax_actual',
                'EshopOrderProduct.price_suggested_taxless',
                'EshopOrderProduct.tax_suggested',
                'EshopOrderProduct.amount',
                'EshopOrderProduct.tax_rate',
                'EshopOrderProduct.static_attributes',
                'EshopOrderProduct.dynamic_attributes',
            ),
            'joins' => array(
                array(
                    'type' => 'left',
                    'model' => 'EshopOrderProduct',
                )
            ),
            'conditions' => array(
                'EshopOrderProduct.run_eshop_orders_id' => $id
            ),
            'ignoreSoftDeleted' => false,
        ));

        if (!empty($Res->data['run_users_id'])) {
            App::loadModel('Core', 'UserProfile');
            $UserProfile = new UserProfile();
            $affidavit = $UserProfile->findFieldBy(
                'UserProfile.private_only_alcohol_orders_affidavit',
                'UserProfile.run_users_id',
                $Res->data['run_users_id']
            );
            $Res->data['private_only_alcohol_orders_affidavit'] = 
                $UserProfile->getFileFieldUrlPath(
                    'private_only_alcohol_orders_affidavit',
                    $affidavit
                );
        }

        return $Res->toJson();
    }
    
    public function admin_loadWithItemsForRemax() {
        $Res = new ExtResponse();
        $Res->success = true;
        $EshopOrder = new EshopOrder();
        
        $id = @$_POST['orderId'];
        if (empty($id) || !is_numeric($id)) {
            $Res->success = false;
            $Res->message = array(
                'text' => __a(__FILE__, 'Wrong identifikator of order id'),
                'type' => 'error'
            );
            return $Res->toJson();
        }

        $data = $EshopOrder->findFirst(array(
            'conditions' => array(
                'id' => $id
            ),
        ));
        $orderLang = $EshopOrder->getLangByMerchantPid($data['merchant_pid']);
        App::setI18n($orderLang);        
        
        if (!empty($data['delivery_fullname'])) {
            $data['fullname'] = $data['delivery_fullname'];
        }
        if (!empty($data['delivery_street'])) {
            $data['street'] = $data['delivery_street'];
        }
        if (!empty($data['delivery_zip'])) {
            $data['zip'] = $data['delivery_zip'];
        }
        if (!empty($data['delivery_city'])) {
            $data['city'] = $data['delivery_city'];
        }
        if (!empty($data['delivery_country'])) {
            $data['country'] = $data['delivery_country'];
        }
        if (!empty($data['delivery_phone'])) {
            $data['phone'] = $data['delivery_phone'];
        }             
        $data['country'] = Eshop::getCountryCode($data['country']);

        if (!empty($data['products_price_suggested_taxless'])) {
            $data['value'] = $data['products_price_suggested_taxless'] + $data['products_tax_suggested'];
        }
        else {
            $data['value'] = $data['products_price_actual_taxless'] + $data['products_tax_actual'];
        }
        if (Eshop::getActualCurrency('decimals') == 0) {
            $data['value'] = ceil($data['value']);
        }
        else {
            $data['value'] = number_format($data['value'], 2, ',', '');
        }

        if (
            $data['run_payment_methods_id'] == 2
            ||
            // in case of rumovabanke, the cod delivery is identified by no payment id
            // (see EshopOrder::addExternal()
            $data['merchant_pid'] === 'rumovabanka'
            && empty($data['run_payment_methods_id'])
        ) {
            // jedna sa o dobierku
            if (!empty($data['order_price_suggested_taxless'])) {
                $data['dobierka'] = $data['order_price_suggested_taxless'] + $data['order_tax_suggested'];
            }
            else {
                $data['dobierka'] = $data['order_price_to_pay'];
            }
            $data['dobierka'] = number_format($data['dobierka'], 2, ',', '');
            $data['dobierka_mena'] = 'EUR';
            $data['dobierka_cislo_uctu'] = 'SK0983200000001300001050';
            if ($data['country'] == 'CZ') {
                $data['dobierka_mena'] = 'CZK';
                $data['dobierka_cislo_uctu'] = 'CZ8858000000000002110828';
            }
        }

        if ($data['run_payment_methods_id'] > 2) {
            // jedna sa o platbu vopred
            $data['dodaci_list'] = 'ano';
        }
        
        $data['description'] = __a(__FILE__, 'krehké, opatrná manipulácia');

        $Res->data = $data;
        
        // it is allowed to order many shipments for an order (it can be splitted to many
        // packages). To avoid unintentional double submit of the same order show an alert 
        // in case that shipment has been already ordered.
        if (!empty($data['shipment_ordered'])) {
            $Res->success = false; // to show message as alert message
            $Res->message = __v(__FILE__, 'Preprava pre túto objednávku už bola objednaná');
        }
        
        return $Res->toJson();
    }

    /**
     * Order ReMax shipping via ReMax API
     * 
     * Ask Remax to get documentation: https://dispatch.remax.sk/dispatch/pages/menu.php?p=login
     *
     * Method is called from ExtJs via AJAX
     * Method send data submited from ExtJs form and
     * return answer from ReMax API.
     * Answer is showed in ExtJs form.
     */
    public function admin_orderRemax() {
        $Res = new ExtResponse();

        // validation
        if (!empty($this->data['dobierka'])) {
            if (empty($this->data['vs'])) {
                $Res->success = false;
                $Res->message = 'Nevyplnili ste Variabilný symbol.';
                return $Res->toJson();
            }
            if (empty($this->data['dobierka_vs'])) {
                $Res->success = false;
                $Res->message = 'Nevyplnili ste Variabilný symbol dobierka.';
                return $Res->toJson();
            }
        }

        $tomorrow = time() + 24 * 60 * 60;
        
        $EshopOrder = new EshopOrder();
        $merchantPid = $EshopOrder->findField('merchant_pid', array(
            'conditions' => array(
                'id' => $this->data['id'],
            )
        ));

        $orderLang = $EshopOrder->getLangByMerchantPid($merchantPid);
        $originalLang = App::setI18n($orderLang);
        if ($orderLang === 'cs') {
            $service = "CZ ECO";
            $dodaciList = 'nie';
            // Cena sa musí prepočítať na EUR podľa czkConversionRate
            $value = $this->data['value'] / $this->getSetting('czkConversionRate'); 
        }
        elseif ($orderLang === 'hu') {
            throw new Exception(__e(__FILE__, 'Export do Remaxu pre .hu je potrebné naprogramovať'));
        }
        else {
            $service = "SK Pack";
            $dodaciList = $this->data['dodaci_list'];
            $value = $this->data['value'];
        }

        // prepare data
        $data = array(
            "login" => "KXXXXX57P",
            "password" => "NiWaQaQH",

            "odos_nazov" => "K & K EXPORT - IMPORT, s.r.o.",
            "odos_mesto" => "Martin",
            "odos_ulica_cislo" => "Čsl. armády 5752/1",
            "odos_psc" => "03601",
            "odos_stat" => "SK",
            "odos_kontakt" => "0905 552 826, 0905 472 804",
            "odos_kon_osoba" => "Eleonóra Ločajová",
            "odos_poznamka" => "",

            "prij_nazov" => $this->data['fullname'],
            "prij_mesto" => $this->data['city'],
            "prij_ulica_cislo" => $this->data['street'],
            "prij_psc" => $this->data['zip'],
            "prij_stat" => $this->data['country'],
            "prij_kontakt" => $this->data['phone'],
            "prij_kon_osoba" => $this->data['contact_person'],
            "prij_poznamka" => $this->data['comment'],

            "zas_hodnota" => $value,
            "zas_hmotnost" => str_replace(',', '.', $this->data['weight']),
            "pocet_kusov" => $this->data['package_count'],
            "variabilny_symbol" => $this->data['vs'],
            "variabilny_symbol_dobierka" => $this->data['dobierka_vs'],

            "odos_den" => date('d'),
            "odos_mes" => date('m'),
            "odos_rok" => date('Y'),
            "odos_hod" => date('H'),
            "odos_min" => date('i'),
            "odos_hod2" => date('H'),
            "odos_min2" => date('i'),

            "prij_den" => date('d', $tomorrow),
            "prij_mes" => date('m', $tomorrow),
            "prij_rok" => date('Y', $tomorrow),
            "prij_hod" => date('H', $tomorrow),
            "prij_min" => date('i', $tomorrow),
            "prij_hod2" => date('H', $tomorrow),
            "prij_min2" => date('i', $tomorrow),

            "typ" => "balik",
            "popis" => $this->data['description'],
            "dodaci_list" => $dodaciList,
            "typ_platby" => "fakt",
            "dobierka" => str_replace(',', '.', $this->data['dobierka']),
            "sluzba" => $service, 
            "dobierka_ucet" => $this->data['dobierka_cislo_uctu'],

            "odos_notif_email" => "", //(string)$EshopOrder->getCopyEmail($merchantPid, false),
            "prij_notif_email" => $this->data['email'],
            "prij_notif_zas_stav" => "nie,nie,nie,nie,nie",
            "prij_notif_zas_stav_email" => "",

            "doplnkove_sluzby" => "1",
            "id_pobocky_zasielkovna" => "",
            "dobierka_mena" => $this->data['dobierka_mena'],
        );

        // send request to API via SOAP

        App::loadLib('Core', 'nusoap_1_114/nusoap.php');

        // Create the client instance
        $client = new php_nusoapclient('https://dispatch.remax.sk/dispatch/soap/pridaj_zakazku3.php');

        // Check for an error
        $err = $client->getError();
        if ($err) {
            $Res->success = false;
            $Res->message = 'Systémová chyba: ' . $err;
        } else {

            $client->soap_defencoding = 'UTF-8';

            if (ON_LOCALHOST/*!empty($_COOKIE['run_developer'])*/) {
                $result = 'OK: Akože je to OK.';
            } else {
                $result = $client->call(
                    "pridajZakazku",      // method name
                    $data,                // data
                    'uri:zakazka',        // namespace
                    'uri:zakazka/pridaj'  // SOAPAction
                );
            }

            // Check for a fault
            if ($client->fault) {
                $Res->success = false;
                $Res->message = 'Systémová chyba: ' . $result;
            } else {
                // Check for errors
                $err = $client->getError();
                if ($err) {
                    $Res->success = false;
                    $Res->message = 'Systémová chyba: ' . $err;
                } else {
                    $res = substr($result, 0, 2);
                    $mess = $result;
                    if ($res == 'OK' || $res == 'KO') {
                        $mess = trim(substr($mess, 3));
                    }
                    if ($res == 'OK') {
                        $Res->success = true;

                        // write remax date and change status of order
                        $EshopOrder->save(array(
                            'id' => $this->data['id'],
                            'shipment_ordered' => date('Y-m-d H:i:s'),
                            'status' => 'enum_shipped_order',
                        ));
                        
                        //
                        // send notification email
                        //
                        if ($merchantPid !== 'rumovabanka') {
                            $msgBody = $this->getSetting(
                                'EshopOrder.msgBodyStatusChange.enum_shipped_order'
                            );
                        }
                        else {
                            $msgBody = $this->getSetting(
                                'EshopOrder.msgBodyStatusChangeRB.enum_shipped_order'
                            );
                        }
                        if (empty($msgBody)) {
                            if ($merchantPid !== 'rumovabanka') {
                                $msgBody = $this->getSetting(
                                    'EshopOrder.msgBodyStatusChange'
                                );
                            }
                            else {
                                $msgBody = $this->getSetting(
                                    'EshopOrder.msgBodyStatusChangeRB'
                                );
                            }
                        }

                        $msgSubject = $this->getSetting(
                            'EshopOrder.msgSubjectStatusChange.enum_shipped_order'
                        );
                        if (empty($msgSubject)) {
                            $msgSubject = $this->getSetting(
                                'EshopOrder.msgSubjectStatusChange'
                            );
                        }
                        $hasSuggestedPrices = false;
                        $inserts = $EshopOrder->getInserts($this->data['id'], $hasSuggestedPrices);
                        if (!empty($inserts[':userEmail:']) && !ON_LOCALHOST) {
                            if ($EshopOrder->sendEmail(
                                $this->data['id'],
                                $msgBody,
                                $inserts[':userEmail:'],
                                array(
                                    'subject' => $msgSubject,
                                    'from' => $EshopOrder->getSenderEmail($merchantPid),
                                    'cc' => $EshopOrder->getCopyEmail($merchantPid),
                                    'inserts' => $inserts,
                                    'embedImages' => true,
                                )
                            )) {
                                $mess .= '<br>Objednávka prešla do stavu Odoslaná a klientovi bol odoslaný notifikačný email.';
                            } else {
                                $mess .= '<br>Objednávka prešla do stavu Odoslaná, ale klientovi sa nepodarilo odoslať notifikačný email.';
                            }
                        }
                    } else {
                        $Res->success = false;
                    }
                    $Res->message = $mess;
                }
            }
        }
        $Res->message = str_replace("\n", '<br>', $Res->message);
        $Res->message = str_replace(';', '<br>', $Res->message);
        $Res->message = str_replace('\n', '<br>', $Res->message);
        
        if (!empty($err)) {
            App::logError('Remax order submition failure', array(
                'var' => $err,
                'email' => true,
            ));
        }
        
        App::setI18n($originalLang);

        return $Res->toJson();
    }
    
    public function admin_loadWithItemsFor123Kurier() {
        $Res = new ExtResponse();
        $Res->success = true;
        $EshopOrder = new EshopOrder();
        
        $id = @$_POST['orderId'];
        if (empty($id) || !is_numeric($id)) {
            $Res->success = false;
            $Res->message = array(
                'text' => __a(__FILE__, 'Wrong identifikator of order id'),
                'type' => 'error'
            );
            return $Res->toJson();
        }

        $data = $EshopOrder->findFirst(array(
            'conditions' => array(
                'id' => $id
            ),
        ));
        $orderLang = $EshopOrder->getLangByMerchantPid($data['merchant_pid']);
        App::setI18n($orderLang);
        
        if (!empty($data['delivery_fullname'])) {
            $data['fullname'] = $data['delivery_fullname'];
        }
        if (!empty($data['delivery_street'])) {
            $data['street'] = $data['delivery_street'];
        }
        if (!empty($data['delivery_zip'])) {
            $data['zip'] = $data['delivery_zip'];
        }
        if (!empty($data['delivery_city'])) {
            $data['city'] = $data['delivery_city'];
        }
        if (!empty($data['delivery_country'])) {
            $data['country'] = $data['delivery_country'];
        }
        if (!empty($data['delivery_phone'])) {
            $data['phone'] = $data['delivery_phone'];
        }             

        $data['reference'] = $data['number'];
        
        if (!empty($data['products_price_suggested_taxless'])) {
            $data['value'] = $data['products_price_suggested_taxless'] + $data['products_tax_suggested'];
        }
        else {
            $data['value'] = $data['products_price_actual_taxless'] + $data['products_tax_actual'];
        }
        // see field hint
        if (
            $data['value'] < 2000
            || $orderLang === 'cs'
        ) {
            $data['value'] = '';
        }
        elseif (Eshop::getActualCurrency('decimals') == 0) {
            $data['value'] = ceil($data['value']);
        }
        else {
            $data['value'] = number_format($data['value'], 2, ',', '');
        }

        if ($data['run_payment_methods_id'] == 2) {
            // jedna sa o dobierku
            if (!empty($data['order_price_suggested_taxless'])) {
                $data['dobierka'] = $data['order_price_suggested_taxless'] + $data['order_tax_suggested'];
            }
            else {
                $data['dobierka'] = $data['order_price_to_pay'];
            }
            $data['dobierka'] = number_format($data['dobierka'], 2, ',', '');
            //$data['dobierka_mena'] = 'EUR';
            //$data['dobierka_cislo_uctu'] = 'SK0983200000001300001050';
        }
        
        $data['description'] = __a(__FILE__, 'krehké, opatrná manipulácia');

        $Res->data = $data;
        
        // it is allowed to order many shipments for an order (it can be splitted to many
        // packages). To avoid unintentional double submit of the same order show an alert 
        // in case that shipment has been already ordered.
        if (!empty($data['shipment_ordered'])) {
            $Res->success = false; // to show message as alert message
            $Res->message = __v(__FILE__, 'Preprava pre túto objednávku už bola objednaná');
        }
        
        return $Res->toJson();
    }

    /**
     * Order 123Kurier shipping via 123Kurier API
     * 
     * Method is called from ExtJs via AJAX
     * Method send data submited from ExtJs form and
     * return answer from 123Kurier API.
     * Answer is showed in ExtJs form.
     */
    public function admin_order123Kurier() {
        $Res = new ExtResponse();

        $EshopOrder = new EshopOrder();
        
        $order = $EshopOrder->findFirst(array(
            'conditions' => array(
                'id' => $this->data['id'],
            ),
        ));
        $orderLang = $EshopOrder->getLangByMerchantPid($order['merchant_pid']);
        $originalLang = App::setI18n($orderLang);
                
        $items = array();
        $this->data['package_count'] = abs(intval($this->data['package_count']));
        $this->data['weight'] = floatval(str_replace(',', '.', $this->data['weight']));
        for ($i = 0; $i < $this->data['package_count']; $i++) {
            if (
                $this->data['package_count'] > 0
                && $this->data['weight'] > 0
            ) {                
                $items[] = array(
                    'weight' => $this->data['weight'] / $this->data['package_count'],
                );
            }
            else {
                $items[] = array(
                    'weight' => 1,
                );
            }
        }
        
        // ATTENTION: Set this TRUE for testing
        $useTestAccount = ON_LOCALHOST;
        
        $note = '';
        if (!empty($this->data['comment'])) {
            $note .= Str::uppercaseFirst(trim($this->data['comment'], '. ')) . '. ';
        }
        if (!empty($this->data['description'])) {
            $note .= Str::uppercaseFirst(trim($this->data['description'], '. ')) . '. ';
        }
        $note = trim($note);
        
        $cashOnDelivery = floatval(str_replace(',', '.', $this->data['dobierka']));
        
        $this->data['value'] = intval($this->data['value']);
        
        $reference = $this->data['reference'];
        if (!$reference) {
            $reference = $order['number'];
        }
        
        $orderValue = floatval(str_replace(',', '.', $this->data['value']));
        
        $request = array(
            "orders" => array(
                array(
                    "clientOrderNumber" => $reference, // len interná referencia
                    "dateOfCall" => date('d.m.Y'),
                    "cashOnDelivery" => $cashOnDelivery,
                    "cashOnDeliveryVarSymbol" => $cashOnDelivery ? $this->data['dobierka_vs'] : '',
                    "cashOnDeliveryIban" => $cashOnDelivery ? $this->data['dobierka_cislo_uctu'] : '',
                    "note" => $note,
                    "sender" => array(
                        "id" => $useTestAccount ? 68 : 1522,
                        "type" => 0, //values 0 - adresa podľa id,  2 - adresa na etikete, 3 - bez odosielatela
                        // na základe odporučenia IT technika to nechávane tu aj keď nevyplnené
                        "etiket" => array(
                            "name" => "",
                            "street" => "",
                            "zip" => "",
                            "city" => "",
                            "country" => ""
                        ),
                    ),
                    "recipient" => array(
                        "name" => $this->data['fullname'],
                        "street" => $this->data['street'],
                        "zip" => preg_replace('/\s+/', '', $this->data['zip']),
                        "city" => $this->data['city'],
                        "country" => Eshop::getCountryCode($this->data['country']),
                        "phone" => $this->data['phone'],
                        "contactPerson" => $this->data['contact_person'],
                        "mail" => $this->data['email'],
                    ),
                    "items" => $items,
                    "insurance" => array(
                        "enabled" => intval(!empty($orderValue)),
                        "orderValue" => $orderValue
                    ),
                    "services" => array(
                        // garantované doručenie 1/0 povolené len pre SK (spoplatnené)
                        "gdd" => false,
                        // doručenie do 12 H 1/0 povolené len pre SK (8.9.2020 ešte nie celkom funkčné)
                        "d12" => false,
                        // sms avízo povolené 1/0 povolené len pre SK (zadarmo / zahrnuté v cene služby)
                        "sAdvice" => boolval($this->data['sms_notification']),
                        // telefonické avízo 1/0 povolené len pre SK (???)
                        "tAdvice" => false
                    ),
                ),
            ),
        );
        
        //App::log('test', '$request', array('var' => $request)); //debug
        
//        //debug>
//        // Please, uncomment these lines, if you are adding new courier to drinkcentrum 
//        // and need to mimic functionality of this method.
//        if (ON_LOCALHOST) {
//            $Res->message = 'Volanie z localhostu prebehlo úspešne.';
//            $Res->success = true;
//            // write 123kurier date and change status of order
//            $EshopOrder->save(array(
//                'id' => $this->data['id'],
//                'shipment_ordered' => date('Y-m-d H:i:s'),
//                'status' => 'enum_shipped_order',
//            ));
//            return $Res->toJson();    
//        }
//        //<debug
        
        $errors = array();
        
        try {
            $username = $useTestAccount ? '<EMAIL>' : '<EMAIL>';
            $password = $useTestAccount ? 'api123456' : '<EMAIL>';            
            $ch = curl_init('https://123kurier.jpsoftware.sk/atol-api/order');
            curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
            curl_setopt($ch, CURLOPT_HEADER, 0);
            curl_setopt($ch, CURLOPT_USERPWD, ($username . ':' . $password));
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($request));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
            $result = curl_exec($ch);
            $code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
        } catch (Throwable $e) {
            $Res->success = false;
            $Res->message = $e->getMessage();
            $errors[] = $e->getMessage();
            
            App::logError('123Kurier order submition failure', array(
                'var' => $errors,
                'email' => true,
            ));
            
            return $Res->toJson();
        }
        
        $result = json_decode($result, true);        
        
        if ($code == 200 && !empty($result['orderNumbers']) ) {
            
            $Res->success = true;
            $message = 'Bola vytvorená objednávka ' . implode("", $result['orderNumbers']) . ".";

            // write to shipment_ordered date and change status of order
            $EshopOrder->save(array(
                'id' => $this->data['id'],
                'shipment_ordered' => date('Y-m-d H:i:s'),
                'status' => 'enum_shipped_order',
            ));
            
            //
            // send notification email
            //
            if ($order['merchant_pid'] !== 'rumovabanka') {
                $msgBody = $this->getSetting(
                    'EshopOrder.msgBodyStatusChange.enum_shipped_order'
                );
            }
            else {
                $msgBody = $this->getSetting(
                    'EshopOrder.msgBodyStatusChangeRB.enum_shipped_order'
                );
            }
            if (empty($msgBody)) {
                if ($order['merchant_pid'] !== 'rumovabanka') {
                    $msgBody = $this->getSetting(
                        'EshopOrder.msgBodyStatusChange'
                    );
                }
                else {
                    $msgBody = $this->getSetting(
                        'EshopOrder.msgBodyStatusChangeRB'
                    );
                }
            }
  
            $msgSubject = $this->getSetting(
                'EshopOrder.msgSubjectStatusChange.enum_shipped_order'
            );
            if (empty($msgSubject)) {
                $msgSubject = $this->getSetting(
                    'EshopOrder.msgSubjectStatusChange'
                );
            }
            $hasSuggestedPrices = false;
            $inserts = $EshopOrder->getInserts($this->data['id'], $hasSuggestedPrices);
            if (!empty($inserts[':userEmail:']) && !ON_LOCALHOST) {
                if ($EshopOrder->sendEmail(
                    $this->data['id'],
                    $msgBody,
                    $inserts[':userEmail:'],
                    array(
                        'subject' => $msgSubject,
                        'from' => $EshopOrder->getSenderEmail($order['merchant_pid']),
                        'cc' => $EshopOrder->getCopyEmail($order['merchant_pid']),
                        'inserts' => $inserts,
                        'embedImages' => true,
                    )
                )) {
                    $message .= '<br>Objednávka prešla do stavu Odoslaná a klientovi bol odoslaný notifikačný email.';
                } else {
                    $message .= '<br>Objednávka prešla do stavu Odoslaná, ale klientovi sa nepodarilo odoslať notifikačný email.';
                }
            }
            
            $Res->message = $message; 
        } else {
            $Res->success = false;
            $Res->message = json_encode($result, JSON_UNESCAPED_UNICODE);
            $errors[] = json_encode($result, JSON_UNESCAPED_UNICODE);
        }
        
        if (!empty($errors)) {
            App::logError('123Kurier order submition failure', array(
                'var' => $errors,
                'email' => true,
            ));
        }
        
        App::setI18n($originalLang);

        return $Res->toJson();
    }
    
    public function admin_loadWithItemsForPacketa() {
        $Res = new ExtResponse();
        $Res->success = true;
        $EshopOrder = new EshopOrder();
        
        $id = @$_POST['orderId'];
        if (empty($id) || !is_numeric($id)) {
            $Res->success = false;
            $Res->message = array(
                'text' => __a(__FILE__, 'Wrong identifikator of order id'),
                'type' => 'error'
            );
            return $Res->toJson();
        }

        $data = $EshopOrder->findFirst(array(
            'conditions' => array(
                'id' => $id
            ),
        ));
        $orderLang = $EshopOrder->getLangByMerchantPid($data['merchant_pid']);
        App::setI18n($orderLang);
        
        // use delivery address if this is a case of delivery to address (no pickup 
        // place is selected) and if there is a delivery address provided
        if (empty($data['pickup_place_id'])) {
            if (!empty($data['delivery_fullname'])) {
                $data['fullname'] = $data['delivery_fullname'];            
            }
            if (!empty($data['delivery_street'])) {
                $data['street'] = $data['delivery_street'];
            }
            if (!empty($data['delivery_zip'])) {
                $data['zip'] = $data['delivery_zip'];
            }
            if (!empty($data['delivery_city'])) {
                $data['city'] = $data['delivery_city'];
            }
            if (!empty($data['delivery_country'])) {
                $data['country'] = $data['delivery_country'];
            }
            if (!empty($data['delivery_phone'])) {
                $data['phone'] = $data['delivery_phone'];
            }             
        }
        
        // normalize data
        if (!empty($data['fullname'])) {
            $fullnameParts = preg_split('/\s+/', $data['fullname']);
            $data['last_name'] = $data['first_name'] = array_pop($fullnameParts);
            if ($fullnameParts) {
                $data['first_name'] = implode(' ', $fullnameParts);
            }
        }
        $match = null;
        if (
            !empty($data['street'])
            && preg_match('#^(.+)\s+([0-9a-z/]+)$#i', $data['street'], $match)
        ) {
            $data['street'] = $match[1];
            $data['house_number'] = $match[2];
        }
        

        $data['reference'] = $data['number'];
        
        if (!empty($data['order_price_suggested_taxless'])) {
            $data['value'] = $data['order_price_suggested_taxless'] + $data['order_tax_suggested'];
        }
        else {
            $data['value'] = $data['order_price_to_pay'];
        }
        if (Eshop::getActualCurrency('decimals') == 0) {
            $data['value'] = ceil($data['value']);
        }
        else {
            $data['value'] = number_format($data['value'], 2, ',', '');
        }

        if ($data['run_payment_methods_id'] == 2) {
            // jedna sa o dobierku
            if (!empty($data['order_price_suggested_taxless'])) {
                $data['dobierka'] = $data['order_price_suggested_taxless'] + $data['order_tax_suggested'];
            }
            else {
                $data['dobierka'] = $data['order_price_to_pay'];
            }
            $data['dobierka'] = number_format($data['dobierka'], 2, ',', '');
        }
        
        $data['description'] = __a(__FILE__, 'fragile');

        $Res->data = $data;
        
        // it is allowed to order many shipments for an order (it can be splitted to many
        // packages). To avoid unintentional double submit of the same order show an alert 
        // in case that shipment has been already ordered.
        if (!empty($data['shipment_ordered'])) {
            $Res->success = false; // to show message as alert message
            $Res->message = __v(__FILE__, 'Preprava pre túto objednávku už bola objednaná');
        }
        
        return $Res->toJson();
    }

    /**
     * Order Packeta shipping via Packeta API
     * 
     * See https://docs.packetery.com/03-creating-packets/06-packetery-api-reference.html#toc-createpacket
     * 
     * Method is called from ExtJs via AJAX
     * Method sends data submited from ExtJs form and returns answer from Packeta API.
     * Answer is showed in ExtJs form.
     * 
     * ATTENTION: Create sender in https://client.packeta.com/ > "Informácie o používateľovi" > "Odosielatelia"
     * and set it (value of "Označenie") in method code under 'eshop' key.
     * 
     * 211012 (<EMAIL>): Packeta API neumožňuje vytvárať zásielky 
     * v stave "Rozpracované". Jediná možnosť podania cez API je priamo do "Podané". 
     * Cez XML/CSV import to však je možné.
     * 
     * Mobilná aplikácia na sledovanie zásielky: 
     * - https://www.packeta.sk/mobilna-aplikacia
     * - https://www.zasilkovna.cz/mobilni-aplikace
     */
    public function admin_orderPacketa() {
        $Res = new ExtResponse();

        $EshopOrder = new EshopOrder();
        
        $order = $EshopOrder->findFirst(array(
            'conditions' => array(
                'id' => $this->data['id'],
            ),
        ));

        $orderLang = $EshopOrder->getLangByMerchantPid($order['merchant_pid']);
        $originalLang = App::setI18n($orderLang);
        
        $errors = array();
        try {
            $reference = $this->data['reference'];
            if (!$reference) {
                $reference = $order['number'];
            }

            $cashOnDelivery = floatval(str_replace(',', '.', $this->data['dobierka']));

            $orderValue = floatval(str_replace(',', '.', $this->data['value']));

            $countryCode = Eshop::getCountryCode($this->data['country']);
            // if delivery to pickup place
            $externalPickupPlaceId = null;
            if (!empty($this->data['pickup_place_id'])) {
                $separatorPosition = strpos($this->data['pickup_place_id'], ':');
                if ($separatorPosition !== false) {
                    // external carrier id
                    $branchId = substr($this->data['pickup_place_id'], 0, $separatorPosition);
                    // external carrier pickup place id
                    $externalPickupPlaceId = substr($this->data['pickup_place_id'], $separatorPosition + 1);
                }
                else {
                    $branchId = $this->data['pickup_place_id'];
                }
            }
            // if delivery to address then set id of Packeta home service
            elseif ($countryCode === 'SK') {
                $branchId = 131; // SK Packeta Home HD (see https://client.packeta.com/sk/packet-drafts/ > Výdajné miesto alebo dopravca
            }
            elseif ($countryCode === 'CZ') {
                $branchId = 106; // CZ Packeta Home HD (see https://client.packeta.com/sk/packet-drafts/ > Výdajné miesto alebo dopravca
            }
            elseif ($countryCode === 'HU') {
                $branchId = 4159; // HU Doručenie na adresu HD (see https://client.packeta.com/sk/packet-drafts/ > Výdajné miesto alebo dopravca
            }
            else {
                throw new Exception(__v(
                    __FILE__,
                    'Pre štát "%s" nie je definovaný dopravca. Kontaktujte prosím programátora :)',
                    $this->data['country']
                ));
            }
            
            $weight = floatval(str_replace(',', '.', $this->data['weight']));
            if (
                $countryCode === 'CZ' 
                && $weight > 10
            ) {
                throw new Exception(__v(
                    __FILE__,
                    'Max povolená hmotnosť balíka pre štát "%s" je 10 kg',
                    $this->data['country']
                ));
            }
            
            $note = '';
            if (!empty($this->data['comment'])) {
                $note .= Str::uppercaseFirst(trim($this->data['comment'], '. ')) . '. ';
            }
            if (!empty($this->data['description'])) {
                $note .= Str::uppercaseFirst(trim($this->data['description'], '. ')) . '. ';
            }
            $note = trim($note);

            $request = array(
                'number' => $reference,
                'name' => trim($this->data['first_name']),
                'surname' => trim($this->data['last_name']),
                'email' => trim($this->data['email']),
                'phone' => trim($this->data['phone']),
                'addressId' => $branchId, // country is identified /set by $branchId - see here above
                'street' => trim($this->data['street']),
                'houseNumber' => trim($this->data['house_number']),
                'zip' => preg_replace('/\s+/', '', $this->data['zip']),
                'city' => trim($this->data['city']),
                'cod' => $cashOnDelivery,
                'value' => $orderValue,
                'weight' => $weight,
                'note' => $note . ' ', // client adds invoice number to note, so make it as easy as possible
                // ATTENTION: This serves to choose propper sender (Odosielateľ) and if must match 
                // value of column "Označenie" in https://client.packeta.com/ > "Informácie o používateľovi"
                // > "Odosielatelia"
                'eshop' => $EshopOrder->getDomainByMerchantPid($order['merchant_pid']),
            );
            if (!empty($externalPickupPlaceId)) {
                $request['carrierPickupPoint'] = $externalPickupPlaceId;
            }
                
        //App::log('test', '$request', array('var' => $request)); //debug
        
//        //debug>
//        // Please, uncomment these lines, if you are adding new courier to drinkcentrum 
//        // and need to mimic functionality of this method.
//        if (ON_LOCALHOST) {
//            $Res->message = 'Volanie z localhostu prebehlo úspešne.';
//            $Res->success = true;
//            // write Packeta date and change status of order
//            $EshopOrder->save(array(
//                'id' => $this->data['id'],
//                'shipment_ordered' => date('Y-m-d H:i:s'),
//                'status' => 'enum_shipped_order',
//            ));
//            return $Res->toJson();    
//        }
//        //<debug

            Html::startCapture();
            ?><createPacket><?php
                ?><apiPassword><?php  
                    echo 'a3848682771d0cff94393bc0528e243f';
                ?></apiPassword><?php
                ?><packetAttributes><?php
                    foreach ($request as $field => $value) {
                        echo '<' . $field . '>' . $value . '</' . $field . '>';
                    }
                ?></packetAttributes><?php
            ?></createPacket><?php
            $xml = Html::endCapture();
            
            $result = App::request('https://www.zasilkovna.cz/api/rest', array(
                'method' => 'post',
                'header' => array('Content-type: text/xml'),
                'data' => $xml,
            ));
            
            $result = Utility::convertXmlToArray($result);
            
            if (App::$slug === '_debug') {
                $debugOutput = '';
                $debugOutput .= '<pre>' . print_r($xml, true) . '</pre><br><br>';
                $debugOutput .= '<pre>' . print_r($result, true) . '</pre><br><br>';
                return $debugOutput;
            }
            /*/debug>
            else {
                App::log('admin_orderPacketa', 'Data & Response', array(
                    'var' => array(
                        'data' => json_encode($xml, JSON_UNESCAPED_UNICODE),
                        'response' => json_encode($result, JSON_UNESCAPED_UNICODE),
                    )
                ));
            }
            //*/
        }
        catch(Throwable $e) {
            $Res->success = false;
            $Res->message = $e->getMessage();
            $errors[] = $e->getMessage();
            
            App::logError('Packeta order submition failure', array(
                'var' => $errors,
                'email' => true,
            ));
            
            return $Res->toJson();
        }
        
        if (
            !empty($result['status'])
            && strtolower($result['status']) === 'ok'
        ) {            
            $Res->success = true;
            $message = 'Bola vytvorená objednávka ' . $result['result']['id'] . ".";
            if (empty($order['shipment_numbers'])){
                $shipmentNumbers = $result['result']['barcode'];
            }
            else {
                $shipmentNumbers = $order['shipment_numbers'] . ', ' . $result['result']['barcode'];
            }

            // write to shipment_ordered date and change status of order
            $EshopOrder->save(array(
                'id' => $this->data['id'],
                'shipment_ordered' => date('Y-m-d H:i:s'),
                'status' => 'enum_shipped_order',
                'shipment_numbers' => $shipmentNumbers,
            ));
            
            //
            // send notification email
            //
            if ($order['merchant_pid'] !== 'rumovabanka') {
                $msgBody = $this->getSetting(
                    'EshopOrder.msgBodyStatusChange.enum_shipped_order'
                );
            }
            else {
                $msgBody = $this->getSetting(
                    'EshopOrder.msgBodyStatusChangeRB.enum_shipped_order'
                );
            }
            if (empty($msgBody)) {
                if ($order['merchant_pid'] !== 'rumovabanka') {
                    $msgBody = $this->getSetting(
                        'EshopOrder.msgBodyStatusChange'
                    );
                }
                else {
                    $msgBody = $this->getSetting(
                        'EshopOrder.msgBodyStatusChangeRB'
                    );
                }
            }
  
            $msgSubject = $this->getSetting(
                'EshopOrder.msgSubjectStatusChange.enum_shipped_order'
            );
            if (empty($msgSubject)) {
                $msgSubject = $this->getSetting(
                    'EshopOrder.msgSubjectStatusChange'
                );
            }
            $hasSuggestedPrices = false;
            $inserts = $EshopOrder->getInserts($this->data['id'], $hasSuggestedPrices);

            // add :shippedInfo: insert
            $shippedInfo = '<br><br>';
            if (
                !empty($this->data['many_packages'])
                || count(explode(',', $shipmentNumbers)) > 1
            ) {
                $shippedInfo .= __(__FILE__, 'Vaša objednávka bude doručovaná vo viac ako jednom balíku.') . ' ';
            }
            $inserts[':shippedInfo:'] = $shippedInfo . __(__FILE__, 'Prosím pozorne sledujte správy od dopravcu a nezabudnite si skontrolovať aj priečinky spam alebo nevyžiadaná pošta.');

            if (!empty($inserts[':userEmail:']) && !ON_LOCALHOST) {
                if ($EshopOrder->sendEmail(
                    $this->data['id'],
                    $msgBody,
                    $inserts[':userEmail:'],
                    array(
                        'subject' => $msgSubject,
                        'from' => $EshopOrder->getSenderEmail($order['merchant_pid']),
                        'cc' => $EshopOrder->getCopyEmail($order['merchant_pid']),
                        'inserts' => $inserts,
                        'embedImages' => true,
                    )
                )) {
                    $message .= '<br>Objednávka prešla do stavu Odoslaná a klientovi bol odoslaný notifikačný email.';
                } else {
                    $message .= '<br>Objednávka prešla do stavu Odoslaná, ale klientovi sa nepodarilo odoslať notifikačný email.';
                }
            }
            
            $Res->message = $message; 
        } 
        elseif (
            !empty($result['status'])
            && $result['status'] === 'fault'
            && !empty($result['detail']['attributes']['fault'])
        ) {
            
            $Res->success = false;
            $Res->message = '';
            if (Validate::assocArray($result['detail']['attributes']['fault'])) {
                $Res->message .= $result['detail']['attributes']['fault']['fault'];
            }
            else {
                foreach ($result['detail']['attributes']['fault'] as $validationError) {
                    if ($Res->message) {
                        $Res->message .= '<br>';
                    }
                    $Res->message .= $validationError['fault'];
                }
            }
        }
        else {
            $Res->success = false;
            $Res->message = __v(__FILE__, 'Neznáma chyba') . ':<br>' . 
                json_encode($result, JSON_UNESCAPED_UNICODE);
            $errors[] = json_encode($result, JSON_UNESCAPED_UNICODE);
        }
        
        if (!empty($errors)) {
            App::logError('Packeta order submition failure', array(
                'var' => $errors,
                'email' => true,
            ));
        }
        
        App::setI18n($originalLang);

        return $Res->toJson();
    }
    
    /**
     * Slovak Parcel Service
     */
    public function admin_loadWithItemsForSps() {
        $Res = new ExtResponse();
        $Res->success = true;
        $EshopOrder = new EshopOrder();
        
        $id = @$_POST['orderId'];
        if (empty($id) || !is_numeric($id)) {
            $Res->success = false;
            $Res->message = array(
                'text' => __a(__FILE__, 'Wrong identifikator of order id'),
                'type' => 'error'
            );
            return $Res->toJson();
        }

        $data = $EshopOrder->findFirst(array(
            'conditions' => array(
                'id' => $id
            ),
        ));
        $orderLang = $EshopOrder->getLangByMerchantPid($data['merchant_pid']);
        App::setI18n($orderLang);
        
        // use delivery address if provided
        if (!empty($data['delivery_fullname'])) {
            $data['fullname'] = $data['delivery_fullname'];            
        }
        if (!empty($data['delivery_street'])) {
            $data['street'] = $data['delivery_street'];
        }
        if (!empty($data['delivery_zip'])) {
            $data['zip'] = $data['delivery_zip'];
        }
        if (!empty($data['delivery_city'])) {
            $data['city'] = $data['delivery_city'];
        }
        if (!empty($data['delivery_country'])) {
            $data['country'] = $data['delivery_country'];
        }
        if (!empty($data['delivery_phone'])) {
            $data['phone'] = $data['delivery_phone'];
        }             
        
        // normalize data
        $data['reference'] = $data['number'];
        
        if (!empty($data['order_price_suggested_taxless'])) {
            $data['value'] = $data['order_price_suggested_taxless'] + $data['order_tax_suggested'];
        }
        else {
            $data['value'] = $data['order_price_to_pay'];
        }
        if (Eshop::getActualCurrency('decimals') == 0) {
            $data['value'] = ceil($data['value']);
        }
        else {
            $data['value'] = number_format($data['value'], 2, ',', '');
        }

        if ($data['run_payment_methods_id'] == 2) {
            // jedna sa o dobierku
            if (!empty($data['order_price_suggested_taxless'])) {
                $data['dobierka'] = $data['order_price_suggested_taxless'] + $data['order_tax_suggested'];
            }
            else {
                $data['dobierka'] = $data['order_price_to_pay'];
            }
            $data['dobierka'] = number_format($data['dobierka'], 2, ',', '');
        }
        
        $data['description'] = __a(__FILE__, 'fragile');

        $Res->data = $data;
        
        // it is allowed to order many shipments for an order (it can be splitted to many
        // packages). To avoid unintentional double submit of the same order show an alert 
        // in case that shipment has been already ordered.
        if (!empty($data['shipment_ordered'])) {
            $Res->success = false; // to show message as alert message
            $Res->message = __v(__FILE__, 'Preprava pre túto objednávku už bola objednaná');
        }
        
        return $Res->toJson();
    }
    
    /**
     * Order Slovak Parcel Service shipping via its API.
     * The ordered shippings yo can see in https://webship.sps-sro.sk/
     * Testing account is testAPI / testAPI123
     * 
     * See http://www.solver.sk/webship/webshipApiDokumentacia.pdf
     * Contact: Lucia Zemanova: programmer, <EMAIL>, 0911 708 556
     * 
     * ATTENTION: PHP SOAP extension must be installed:
     *      sudo apt install php7.2-soap
     *      sudo phpenmod soap
     *      sudo service apache2 restart
     * 
     * Method is called from ExtJs via AJAX
     * Method sends data submited from ExtJs form and returns answer from SPS API.
     * Answer is showed in ExtJs form.
     */
    public function admin_orderSps() {
        $Res = new ExtResponse();
        
        $EshopOrder = new EshopOrder();
        
        $order = $EshopOrder->findFirst(array(
            'conditions' => array(
                'id' => $this->data['id'],
            ),
        ));
        
        $orderLang = $EshopOrder->getLangByMerchantPid($order['merchant_pid']);
        $originalLang = App::setI18n($orderLang);
        
        $errors = array();
        try {
            $reference = $this->data['reference'];
            if (!$reference) {
                $reference = $order['number'];
            }

            $cashOnDelivery = floatval(str_replace(',', '.', $this->data['dobierka']));

            $orderValue = floatval(str_replace(',', '.', $this->data['value']));

            $countryCode = Eshop::getCountryCode($this->data['country']);
            if (
                $countryCode !== 'SK'
                && $countryCode !== 'CZ'
                && $countryCode !== 'HU'
            ) {
                throw new Exception(__v(
                    __FILE__,
                    'Pre štát "%s" nie je definovaný dopravca. Kontaktujte prosím programátora :)',
                    $this->data['country']
                ));
            }
            
            $packagesCount = abs(intval(trim($this->data['package_count'])));
            $weights = Str::explode(' ', preg_replace('/\s+/', ' ', trim($this->data['weight'])));
            if ($packagesCount !== count($weights)) {
                throw new Exception(__v(
                    __FILE__,
                    'Zadajte rovnaký počet hmotností balíkov (oddelených medzerou) ako je počet balíkov'
                ));
            }
            foreach ($weights as &$weight) {
                $normalizedWeight = floatval(str_replace(',', '.', $weight));
                if (!$normalizedWeight) {
                    throw new Exception(__v(
                        __FILE__,
                        'Neplatná hodnota "%s" hmotnosti balíka',
                        $weight
                    ));
                }
                $weight = $normalizedWeight;
                if (
                    $countryCode !== 'SK' 
                    && $weight > 10
                ) {
                    throw new Exception(__v(
                        __FILE__,
                        'Max povolená hmotnosť balíka pre štát "%s" je 10 kg',
                        $this->data['country']
                    ));
                }
            }
            unset($weight);
            
            $note = '';
            if (!empty($this->data['comment'])) {
                $note .= Str::uppercaseFirst(trim($this->data['comment'], '. ')) . '. ';
            }
            if (!empty($this->data['description'])) {
                $note .= Str::uppercaseFirst(trim($this->data['description'], '. ')) . '. ';
            }
            $note = trim($note);
            
//            //debug>
//            // Please, uncomment these lines, if you are adding new courier to drinkcentrum 
//            // and need to mimic functionality of this method.
//            if (ON_LOCALHOST) {
//                $Res->message = 'Volanie z localhostu prebehlo úspešne.';
//                $Res->success = true;
//                // write Packeta date and change status of order
//                $EshopOrder->save(array(
//                    'id' => $this->data['id'],
//                    'shipment_ordered' => date('Y-m-d H:i:s'),
//                    'status' => 'enum_shipped_order',
//                ));
//                return $Res->toJson();    
//            }
//            //<debug
            
            // Load libs obtained from SPS
            // 
            // ATTENTION: PHP SOAP extension must be installed:
            //      sudo apt install php7.2-soap
            //      sudo phpenmod soap
            //      sudo service apache2 restart
            //
            // This constant is used by class SoapRequestWebshipAPI()
            define('wsdlLink', 'https://webship.sps-sro.sk/services/WebshipWebService?wsdl');
            $this->loadLib('slovakParcelService/SoapRequestWebshipAPI');            
            $this->loadLib('slovakParcelService/WebServiceAPIClasses');
            $webshipUser = 'K&K';
            $webshipUserPassword = 'K&K';
            //debug>
            if (ON_LOCALHOST || App::$slug === '_debug') {
                $webshipUser = 'testAPI';
                $webshipUserPassword = 'testAPI123';
            }
            //<debug            
            
            $Cod = null;
            if ($cashOnDelivery) {
                $Cod = new Cod(
                    $cashOnDelivery
                );
            }
            
            $packages = array();
            foreach ($weights as $i => $weight) {
                $packageReference = $reference . '-' . str_pad($i + 1, 2, '0', STR_PAD_LEFT);
                $packages[] = new WebServicePackage($packageReference, $weight);
            }
            
            // delivery address
            $deliveryAddressName = trim($this->data['company_name']);
            if (!$deliveryAddressName) {
                $deliveryAddressName = trim($this->data['fullname']);
            }
            $Address = new ShipmentAddress(
                trim($this->data['city']), 
                preg_replace('/\s+/', '', $this->data['zip']), 
                $countryCode, 
                trim($this->data['street']), 
                $deliveryAddressName, 
                trim($this->data['fullname']), 
                trim($this->data['phone']), 
                trim($this->data['email'])
            );
            //create basic shipment with no extra services
            $WebServiceShipment = new WebServiceShipment(
                $Cod,                   // cod
                $orderValue,            // insurvalue
                notifyType::BOTH,       // notifytype
                null,                   // productdesc
                null,                   // recipientpay
                null,                   // returnshipment
                null,                   // saturdayshipment
                serviceName::EXPRESS,   // servicename
                null,                   // tel
                billingUnits::KG,       // units
                $packages,              // packages
                null,                   // pickupaddress
                $Address,               // deliveryaddress
                null,                   // shipmentpickup
                codAttribute::CASH,     // codattribute - client is free to choose payment method but we need to put st here
                null,                   // deliverytype
                null,                   // services
                $note                   // deliveryremark
            );  
            // create request object
            $Shipment = new createCifShipment(
                $webshipUser,
                $webshipUserPassword,
                $WebServiceShipment,
                webServiceShipmentType::TLAC
            );
            // create Webship API wrapper
            $Client = new SoapRequestWebshipAPI();
            // call Webship API 
            $Result = $Client->createCifShipment($Shipment);
            
            //debug>
            if (App::$slug === '_debug') {
                $debugOutput = '';
                //$debugOutput .= Arr::getLiteral($this->data) . '<br><br>';
                $debugOutput .= '<pre>' . print_r($WebServiceShipment, true) . '</pre><br><br>';
                $debugOutput .= '<pre>' . print_r($Result, true) . '</pre><br><br>';
                return $debugOutput;
            }
            //<debug

            // check for soap errors
            if (is_soap_fault($Result)) {
                throw new Exception(__e(__FILE__, 'SOAP error %s: %s', $Result->faultcode, $Result->faultstring));
            }
            // check for erros in response
            elseif (
                ($resultErrors = $Result->getCreateCifShipmentReturn()->getResult()->getErrors())
            ) {
                $resultErrors = trim(implode('. ', (array)$resultErrors));
                if (!$resultErrors) {
                    $resultErrors = __e(__FILE__, 'Neznáma chyba');
                }
                throw new Exception($resultErrors);
            }
            // set success message
            else {
                $Res->success = true;
                $message = __a(
                    __FILE__, 
                    'Bola vytvorená objednávka zásielky pre nasledovné balíky:'
                );
                for (
                    $i = 0; 
                    ($PackageInfo = $Result->getCreateCifShipmentReturn()->getPackageInfo($i));
                    $i++
                ) {
                    $message .= __a(
                        __FILE__, 
                        '<br>- balík č. %s:  %s / %s',
                        $PackageInfo->getPackageNo(),
                        $PackageInfo->getRefNr(),
                        $PackageInfo->getShipNr()
                    );
                }
            }
        }
        catch(Throwable $e) {
            $Res->success = false;
            $Res->message = $e->getMessage();
            $errors[] = $e->getMessage();
            
            App::logError('Slovak Parcel Service order submition failure', array(
                'var' => $errors,
                'email' => true,
            ));
            
            return $Res->toJson();
        }
        

        // write to shipment_ordered date and change status of order
        $EshopOrder->save(array(
            'id' => $this->data['id'],
            'shipment_ordered' => date('Y-m-d H:i:s'),
            'status' => 'enum_shipped_order',
        ));

        //
        // send notification email
        //
        if ($order['merchant_pid'] !== 'rumovabanka') {
            $msgBody = $this->getSetting(
                'EshopOrder.msgBodyStatusChange.enum_shipped_order'
            );
        }
        else {
            $msgBody = $this->getSetting(
                'EshopOrder.msgBodyStatusChangeRB.enum_shipped_order'
            );
        }
        if (empty($msgBody)) {
            if ($order['merchant_pid'] !== 'rumovabanka') {
                $msgBody = $this->getSetting(
                    'EshopOrder.msgBodyStatusChange'
                );
            }
            else {
                $msgBody = $this->getSetting(
                    'EshopOrder.msgBodyStatusChangeRB'
                );
            }
        }

        $msgSubject = $this->getSetting(
            'EshopOrder.msgSubjectStatusChange.enum_shipped_order'
        );
        if (empty($msgSubject)) {
            $msgSubject = $this->getSetting(
                'EshopOrder.msgSubjectStatusChange'
            );
        }
        $hasSuggestedPrices = false;
        $inserts = $EshopOrder->getInserts($this->data['id'], $hasSuggestedPrices);
        if (!empty($inserts[':userEmail:']) && !ON_LOCALHOST) {
            if ($EshopOrder->sendEmail(
                $this->data['id'],
                $msgBody,
                $inserts[':userEmail:'],
                array(
                    'subject' => $msgSubject,
                    'from' => $EshopOrder->getSenderEmail($order['merchant_pid']),
                    'cc' => $EshopOrder->getCopyEmail($order['merchant_pid']),
                    'inserts' => $inserts,
                    'embedImages' => true,
                )
            )) {
                $message .= '<br>Objednávka prešla do stavu Odoslaná a klientovi bol odoslaný notifikačný email.';
            } else {
                $message .= '<br>Objednávka prešla do stavu Odoslaná, ale klientovi sa nepodarilo odoslať notifikačný email.';
            }
        }

        $Res->message = $message; 
        
        App::setI18n($originalLang);

        return $Res->toJson();
    }
    
    public function admin_loadWithItemsForDpd() {
        $Res = new ExtResponse();
        $Res->success = true;
        $EshopOrder = new EshopOrder();
        
        $id = @$_POST['orderId'];
        if (empty($id) || !is_numeric($id)) {
            $Res->success = false;
            $Res->message = array(
                'text' => __a(__FILE__, 'Wrong identifikator of order id'),
                'type' => 'error'
            );
            return $Res->toJson();
        }

        $data = $EshopOrder->findFirst(array(
            'conditions' => array(
                'id' => $id
            ),
        ));
        $orderLang = $EshopOrder->getLangByMerchantPid($data['merchant_pid']);
        App::setI18n($orderLang);
        
        // normalize data
        // - use delivery address if provided BUT only if it is not delivery to 
        // a pickup place (dpdPickup). In case of pickup place keep the original address
        // as a contact address.
        if (!$data['pickup_place_id']) {
            if (!empty($data['delivery_fullname'])) {
                $data['fullname'] = $data['delivery_fullname'];            
            }
            if (!empty($data['delivery_street'])) {
                $data['street'] = $data['delivery_street'];
            }
            if (!empty($data['delivery_zip'])) {
                $data['zip'] = $data['delivery_zip'];
            }
            if (!empty($data['delivery_city'])) {
                $data['city'] = $data['delivery_city'];
            }
            if (!empty($data['delivery_country'])) {
                $data['country'] = $data['delivery_country'];
            }
            if (!empty($data['delivery_phone'])) {
                $data['phone'] = $data['delivery_phone'];
            }
        }
        
        $match = null;
        if (
            !empty($data['street'])
            && preg_match('#^(.+)\s+([0-9a-z/]+)$#i', $data['street'], $match)
        ) {
            $data['street'] = $match[1];
            $data['house_number'] = $match[2];
        }
        
        $data['pickup_date'] = date('j.n.Y');
        
        $data['reference'] = $data['number'];
        
        /*/> there is fixed insurance (according to DPD product) so there is no need to send some order value
        if (!empty($data['order_price_suggested_taxless'])) {
            $data['value'] = $data['order_price_suggested_taxless'] + $data['order_tax_suggested'];
        }
        else {
            $data['value'] = $data['order_price_to_pay'];
        }
        if (Eshop::getActualCurrency('decimals') == 0) {
            $data['value'] = ceil($data['value']);
        }
        else {
            $data['value'] = number_format($data['value'], 2, ',', '');
        }
        //*/
        
        if ($data['run_payment_methods_id'] == 2) {
            // jedna sa o dobierku
            if (!empty($data['order_price_suggested_taxless'])) {
                $data['dobierka'] = $data['order_price_suggested_taxless'] + $data['order_tax_suggested'];
            }
            else {
                $data['dobierka'] = $data['order_price_to_pay'];
            }
            $data['dobierka'] = number_format($data['dobierka'], 2, ',', '');
        }
        
        $data['description'] = __a(__FILE__, 'fragile');

        $Res->data = $data;
        
        // it is allowed to order many shipments for an order (it can be splitted to many
        // packages). To avoid unintentional double submit of the same order show an alert 
        // in case that shipment has been already ordered.
        if (!empty($data['shipment_ordered'])) {
            $Res->success = false; // to show message as alert message
            $Res->message = __v(__FILE__, 'Preprava pre túto objednávku už bola objednaná');
        }
        
        return $Res->toJson();
    }
    
    /**
     * Order DPD shipping via its API.
     * The ordered shippings yo can see in  https://www.dpdshipper.sk/login
     * Testing account has no UI - you cannot login with testing account to above link.
     * 
     * See shipperAPI-doc-2.6-v20212911.pdf (ask <EMAIL> for actual version)
     * Contact: <EMAIL>
     * Account manager: p. Revayová, **********, <EMAIL>
     * 
     * Method is called from ExtJs via AJAX
     * Method sends data submited from ExtJs form and returns answer from SPS API.
     * Answer is showed in ExtJs form.
     */
    public function admin_orderDpd() {
        $Res = new ExtResponse();
        $EshopOrder = new EshopOrder();
        
        $order = $EshopOrder->findFirst(array(
            'conditions' => array(
                'id' => $this->data['id'],
            ),
        ));
        
        App::loadModel('Core', 'User');
        $User = new User();
        //rblsb//$isB2B = $order['business_type'] === 'b2b';
        $isB2B = !empty($order['run_users_id']) 
            && $User->findFieldBy('discount_price_category', 'id', $order['run_users_id']);
        
        $orderLang = $EshopOrder->getLangByMerchantPid($order['merchant_pid']);
        $originalLang = App::setI18n($orderLang);
        
        $errors = array();
        try {
            $reference = $this->data['reference'];
            if (!$reference) {
                $reference = $order['number'];
            }

            $cashOnDelivery = floatval(str_replace(',', '.', $this->data['dobierka']));
            
            /*/> there is fixed insurance (according to DPD product) so there is no need to send some order value
            $orderValue = floatval(str_replace(',', '.', $this->data['value']));
            //*/

            $countryCode = Eshop::getCountryCode($this->data['country']);
            if (
                $countryCode !== 'SK'
                && $countryCode !== 'CZ'
                && $countryCode !== 'HU'
            ) {
                throw new Exception(__v(
                    __FILE__,
                    'Pre štát "%s" nie je definovaný dopravca. Kontaktujte prosím programátora :)',
                    $this->data['country']
                ));
            }
            
            /*/> This does not work correctly as the home country changes according to actual lang
            $this->loadModel('EshopShipmentMethod');
            $Shipment = new EshopShipmentMethod();
            $hasAbroadDelivery = $Shipment->hasAbroadDelivery($order['country']);
            /*/
            $hasAbroadDelivery = $countryCode !== 'SK'; 
            
            $packagesCount = abs(intval(trim($this->data['package_count'])));
            $weights = Str::explode(' ', preg_replace('/\s+/', ' ', trim($this->data['weight'])));
            if ($packagesCount !== count($weights)) {
                throw new Exception(__v(
                    __FILE__,
                    'Zadajte rovnaký počet hmotností balíkov (oddelených medzerou) ako je počet balíkov'
                ));
            }
            foreach ($weights as &$weight) {
                $normalizedWeight = floatval(str_replace(',', '.', $weight));
                if (!$normalizedWeight) {
                    throw new Exception(__v(
                        __FILE__,
                        'Neplatná hodnota "%s" hmotnosti balíka',
                        $weight
                    ));
                }
                $weight = $normalizedWeight;
                if (
                    $hasAbroadDelivery 
                    && $weight > 10
                ) {
                    throw new Exception(__v(
                        __FILE__,
                        'Max povolená hmotnosť balíka pre štát "%s" je 10 kg',
                        $this->data['country']
                    ));
                }
            }
            unset($weight);
            
            $note = '';
            if (!empty($this->data['comment'])) {
                $note .= Str::uppercaseFirst(trim($this->data['comment'], '. ')) . '. ';
            }
            if (!empty($this->data['description'])) {
                $note .= Str::uppercaseFirst(trim($this->data['description'], '. ')) . '. ';
            }
            $note = trim($note);
            
            // delivery address
            $deliveryAddressName = trim($this->data['company_name']);
            if (!$deliveryAddressName) {
                $deliveryAddressName = trim($this->data['fullname']);
            }
            
            $phone = trim($this->data['phone']);
            if (
                substr($phone, 0, 1) !== '+' 
                && substr($phone, 0, 2) !== '00'
            ) {
                $callingCode = Eshop::getCountryCallingCode($countryCode);
                if (!$callingCode) {
                    throw new Exception(__v(__FILE__,  'Zadajte prosím telefón s volacím kódom krajiny (+XX...).'));
                }
                $phone = '+' . $callingCode . ltrim($phone, '0');
            }
            $email = trim($this->data['email']);
            
            $pickupDate = trim($this->data['pickup_date']);
            if (!$pickupDate) {
                throw new Exception(__v(__FILE__, 'Zadajte prosím dátum zvozu'));
            }
            elseif (
                !($pickupDate = Utility::formatDate($pickupDate, 'Ymd'))
            ) {
                throw new Exception(__v(__FILE__, 'Zadajte prosím platný dátum zvozu'));
            }
            elseif ((int)$pickupDate < date('Ymd')) {
                throw new Exception(__v(__FILE__, 'Zadajte prosím dnešný alebo búdúci dátum zvozu'));
            }
            
            /*/>
            //debug>
            // Please, uncomment these lines, if you are adding new courier to drinkcentrum 
            // and need to mimic functionality of this method.
            if (ON_LOCALHOST) {
                $Res->message = 'Volanie z localhostu prebehlo úspešne.';
                $Res->success = true;
                // write Packeta date and change status of order
                $EshopOrder->save(array(
                    'id' => $this->data['id'],
                    'shipment_ordered' => date('Y-m-d H:i:s'),
                    'status' => 'enum_shipped_order',
                ));
                return $Res->toJson();    
            }
            //<debug
            //*/
            
            //
            // send request to DPD
            //
            $apiUrl = 'https://api.dpd.sk/shipment/json';
            $apiUser = '<EMAIL>';
            $apiPassword = 'x71yqwsevslkd6wtdpdve2oh5xzkfkhk';
            $apiCustomerId = 'ZA1249';
            // see https://www.dpdshipper.sk/settings/pickup-addresses
            $apiPickUpAddressId = '101208';
            // see https://www.dpdshipper.sk/settings/bank-accounts
            if ($countryCode === 'SK') {
                $apiBankAccountId = '3741';
            }
            elseif ($countryCode === 'CZ') {
                $apiBankAccountId = '851588';
            }
            elseif ($countryCode === 'HU') {
                $apiBankAccountId = '851589';
            }
            else {
                throw new Exception(__v(
                    __FILE__,
                    'Pre štát "%s" nie je definovaný bankový účet. Kontaktujte prosím programátora :)',
                    $this->data['country']
                ));
            }
            //debug>
            if (ON_LOCALHOST || App::$slug === '_debug') {
                $apiUrl = 'https://capi.dpd.sk/shipment/json';
                $apiUser = '<EMAIL>';
                $apiPassword = 'w5pgb56jyi2sz7q886ow2vufgovhagjn';
                $apiCustomerId = 'ZA1249';
                $apiPickUpAddressId = '114242';
                if ($countryCode === 'SK') {
                    $apiBankAccountId = '3741';
                }
                elseif ($countryCode === 'CZ') {
                    $apiBankAccountId = '11888';
                }
            }
            //<debug
            
            $apiProductId = 9; // DPD Home
            /*/>
            if (
                $order['business_type'] === 'b2b'
                || $hasAbroadDelivery
            ) {
                $apiProductId = 1; // DPD Classic
            }
            //*/
            if (!empty($this->data['pickup_place_id'])) {
                $apiProductId = 17; // ParcelShop Delivery
            }
            
            $deliveryAddressType = 'b2c';
            if (!empty($this->data['pickup_place_id'])) {
                $deliveryAddressType = 'psd';
            }
            elseif ($isB2B) {
                $deliveryAddressType = 'b2b';
            }
            
            // Following parameters are printed on parcel label:
            // - params.shipment.note
            //      (under condition that "Tlačiť poznámku na štítkoch" is checked in dpdshipper.sk)
            // - params.shipment.addressRecipient.reference 
            //      (under condition that "Tlačiť referenčné ID príjemcu" is checked in dpdshipper.sk)
            // - params.shipment.parcels.parcel..reference1
            // - params.shipment.parcels.parcel..reference2
            $data = array(
                'jsonrpc' => '2.0',
                'method' => 'createV3',
                'params' => array(
                    'DPDSecurity' => array(
                        'SecurityToken' => array(
                            'ClientKey' => $apiPassword,
                            'Email' => $apiUser
                        ),
                    ),
                    'shipment' => array(
                        array(
                            'reference' => $reference,
                            'delisId' => $apiCustomerId,
                            'note' => $note,
                            'product' => $apiProductId,
                            'pickup' => array(
                                'date' => $pickupDate,
                                /*/>
                                'timeWindow' => array(
                                    'beginning' => '1400', // HHMM
                                    'end' => '1500', // HHMM
                                ),
                                //*/
                            ),
                            'addressSender' => array(
                                'id' => $apiPickUpAddressId
                            ),
                            'addressRecipient' => array(
                                'type' => $deliveryAddressType,
                                'name' => $deliveryAddressName,
                                'street' => trim($this->data['street']),
                                'houseNumber' => trim($this->data['house_number']),
                                'zip' => preg_replace('/\s+/', '', $this->data['zip']),
                                'country' => Eshop::getCountryCode($this->data['country'], 'isoCode1'),
                                'city' => trim($this->data['city']),
                                'phone' => $phone,
                                'email' => $email,
                            ),
                            // populated here below
                            'parcels' => array( 
                                'parcel' => array(),
                            ),
                            // populated here below
                            'services' => array(),
                        ),
                    ),
                ),
                'id' => 'null'
            );
            
            $dataShipment = &$data['params']['shipment'][0];
            // populate parcels (packages)
            foreach ($weights as $weight) {
                $dataShipment['parcels']['parcel'][] = array(
                    'reference1' => $reference,
                    'weight' => $weight,
                    'height' => '1',
                    'width' => '1',
                    'depth' => '1'
                );
            }
            
            // populate pickup place id
            if (!empty($this->data['pickup_place_id'])) {
                $dataShipment['services']['parcelShopDelivery']['parcelShopId'] = $this->data['pickup_place_id'];
            }
            
            // populate notifications
            if ($phone || $email) {
                $dataShipment['services']['notifications']['notification'] = array();
                if ($phone) {
                    $dataShipment['services']['notifications']['notification'][] = array(
                        'destination' => $phone,
                        // 1 - email
                        // 3 - sms
                        'type' => '3', 
                        // 1 - notification is sent a day before delivery
                        // 904 - notification is sent on a day of delivery 
                        //      and it contains the 1 hour time slot of the predicted delivery time
                        // 902 - notification is sent when the parcel is ready 
                        //      to pick up at the specified parcel shop
                        'rule' => '904', 
                        // use always SK, the final language is resolved by destination country
                        'language' => 'SK', 
                    );
                }
                if ($email) {
                    $dataShipment['services']['notifications']['notification'][] = array(
                        'destination' => $email,
                        'type' => '1', 
                        'rule' => '904', 
                        // use always SK, the final language is resolved by destination country
                        'language' => 'SK',
                    );
                }
            }
            
            // populate cash on delivery
            if ($cashOnDelivery) {
                $dataShipment['services']['cod'] = array(
                    'amount' => $cashOnDelivery,
                    // see the App::setI18n($orderLang) here above
                    'currency' => Eshop::getActualCurrency('code'),
                    'bankAccount' => array(
                        'id' => $apiBankAccountId
                    ),
                    'variableSymbol' => $reference,
                    // 0 - cash only
                    // 1 - card payment enabled (currently allowed for Slovakia only)
                    // 230428 - na základe problému v DPD systéme nám DPD helpdesk 
                    // odporučil používať len hodnotu 0
                    'paymentMethod' => 0,  //$hasAbroadDelivery ? 0 : 1,
                );
            }
            
            // populate saturday delivery
            $saturdayDelivery = (bool)Sanitize::value($this->data['saturday_delivery']);
            if ($saturdayDelivery) {
                $dataShipment['services']['saturdayDelivery'] = $saturdayDelivery;
            }
            
            $responseJson = App::request($apiUrl, array(
                'method' => 'post',
                'header' => array('Content-type: application/json'),
                'data' => json_encode($data),                
            ));
            $response = json_decode($responseJson, true);
            if (App::$slug === '_debug') {
                $debugOutput = '';
                $debugOutput .= '<pre>' . print_r($data, true) . '</pre><br><br>';
                $debugOutput .= '<pre>' . print_r($response, true) . '</pre><br><br>';
                return $debugOutput;
            }
            /*/debug>
            else {
                App::log('admin_orderDpd', 'Data & Response', array(
                    'var' => array(
                        'data' => json_encode($data, JSON_UNESCAPED_UNICODE),
                        'response' => json_encode($response, JSON_UNESCAPED_UNICODE),
                    )
                ));
            }
            //*/
            if (!$response) {
                throw new Exception(__e(__FILE__, 'Invalid response: %s', $responseJson));
            }

            // check for errors
            // - this is undocumented response (deducted just from interaction with API)
            if (!empty($response['error'])) {
                $resultErrors = '';
                if (!empty($response['error']['message'])) {
                    $resultErrors = trim(implode(' ', (array)$response['error']['message']));
                }
                if (!$resultErrors) {
                    $resultErrors = __e(__FILE__, 'Neznáma chyba');
                }
                throw new Exception($resultErrors);
            }
            // - this is (somehow) documented response
            else if (
                empty($response['result']['result'])
                || Validate::assocArray($response['result']['result'])
            ) {
                throw new Exception(__e(__FILE__, 'Invalid response: %s', $responseJson));
            }
            else {
                // regardless to number of parcels (packages) there is always only
                // one item in $response['result']['result'] (according to test requests)
                $responseResult = &$response['result']['result'][0];
                if (empty($responseResult['success'])) {
                    $resultErrors = '';
                    $messages = array();
                    if (!empty($responseResult['messages']['message'])) {
                        $messages = (array)$responseResult['messages']['message'];
                    }
                    else if (!empty($responseResult['messages'])) {
                        $messages = (array)$responseResult['messages'];
                    }
                    if ($messages) {
                        foreach ($messages as &$message) {
                            if (!is_array($message)) {}
                            else if (!empty($message['value'])) {
                                $message = $message['value'];
                            }
                            else {
                                $message = json_encode($message, JSON_UNESCAPED_UNICODE);
                            }
                        }
                        unset($message);
                        $resultErrors = trim(implode(' ', $messages));
                    }
                    if (!$resultErrors) {
                        $resultErrors = __e(__FILE__, 'Neznáma chyba');
                    }
                    throw new Exception($resultErrors);
                }
                // set success message
                else {
                    $Res->success = true;
                    $message = '';
                    if (!empty($responseResult['messages']['message'])) {
                        $message = trim(implode(' ', (array)$responseResult['messages']['message']));
                    }
                    else if (!empty($responseResult['messages'])) {
                        $message = trim(implode(' ', (array)$responseResult['messages']));
                    }
                    if (!$message) {
                        $message = __a(__FILE__, 'Objednávka zásielky bola úspešne vytvorená.');
                    }
                }
            }
        }
        catch(Throwable $e) {
            $Res->success = false;
            $Res->message = $e->getMessage();
            $errors[] = $e->getMessage();
            
            App::logError('Slovak Parcel Service order submition failure', array(
                'var' => $errors,
                'email' => true,
            ));
            
            return $Res->toJson();
        }

        $this->loadModel('EshopShipmentMethod');
        $ShipmentMethod = new EshopShipmentMethod();
        $trackingUrl = $ShipmentMethod->findFieldBy('tracking_url', 'id', $order['run_eshop_shipment_methods_id']);
        $firstShipmentNumber = null; // substr($responseResult['mpsId'], 0, -8)
        $shipmentNumbers = $order['shipment_numbers'];
        foreach ($responseResult['parcels'] as $parcel) {
            if (empty($shipmentNumbers)) {
                $shipmentNumbers = $parcel['parcelno'];
                $firstShipmentNumber = $parcel['parcelno'];
            }
            else {
                $shipmentNumbers .= ', ' . $parcel['parcelno'];
            }
        }
        // write to shipment_ordered date and change status of order
        $EshopOrder->save(array(
            'id' => $this->data['id'],
            'shipment_ordered' => date('Y-m-d H:i:s'),
            'status' => 'enum_shipped_order',
            'delivery_tracking_id' => $firstShipmentNumber,
            'shipment_numbers' => $shipmentNumbers,
            'delivery_tracking_url' => App::rebuildUrl(
                $trackingUrl,
                array(
                    'get' => array(
                        'parcelNumber' => $firstShipmentNumber,
                    ),
                    'mergeGet' => true,
                )
            ),
        ));

        //
        // send notification email
        //
        if ($order['merchant_pid'] !== 'rumovabanka') {
            $msgBody = $this->getSetting(
                'EshopOrder.msgBodyStatusChange.enum_shipped_order'
            );
        }
        else {
            $msgBody = $this->getSetting(
                'EshopOrder.msgBodyStatusChangeRB.enum_shipped_order'
            );
        }
        if (empty($msgBody)) {
            if ($order['merchant_pid'] !== 'rumovabanka') {
                $msgBody = $this->getSetting(
                    'EshopOrder.msgBodyStatusChange'
                );
            }
            else {
                $msgBody = $this->getSetting(
                    'EshopOrder.msgBodyStatusChangeRB'
                );
            }
        }

        $msgSubject = $this->getSetting(
            'EshopOrder.msgSubjectStatusChange.enum_shipped_order'
        );
        if (empty($msgSubject)) {
            $msgSubject = $this->getSetting(
                'EshopOrder.msgSubjectStatusChange'
            );
        }
        $hasSuggestedPrices = false;
        $inserts = $EshopOrder->getInserts($this->data['id'], $hasSuggestedPrices);
        $shippedInfo = '<br><br>';
        if (
            $this->data['packages_count'] > 1
            || !empty($this->data['many_packages'])
            || count(explode(',', $shipmentNumbers)) > 1
        ) {
            $shippedInfo .= __(__FILE__, 'Vaša objednávka bude doručovaná vo viac ako jednom balíku.') . ' ';
        }
        $inserts[':shippedInfo:'] = $shippedInfo . __(__FILE__, 'Prosím pozorne sledujte správy od dopravcu a nezabudnite si skontrolovať aj priečinky spam alebo nevyžiadaná pošta.');
        unset($shippedInfo);
        if (!empty($inserts[':userEmail:']) && !ON_LOCALHOST) {
            if ($EshopOrder->sendEmail(
                $this->data['id'],
                $msgBody,
                $inserts[':userEmail:'],
                array(
                    'subject' => $msgSubject,
                    'from' => $EshopOrder->getSenderEmail($order['merchant_pid']),
                    'cc' => $EshopOrder->getCopyEmail($order['merchant_pid']),
                    'inserts' => $inserts,
                    'embedImages' => true,
                )
            )) {
                $message .= '<br>Objednávka prešla do stavu Odoslaná a klientovi bol odoslaný notifikačný email.';
            } else {
                $message .= '<br>Objednávka prešla do stavu Odoslaná, ale klientovi sa nepodarilo odoslať notifikačný email.';
            }
        }

        $Res->message = $message; 
        
        App::setI18n($originalLang);

        return $Res->toJson();
    }

    /**
     * Load order data for WoltDrive shipping
     * Method is called from ExtJs via AJAX
     */
    public function admin_loadWithItemsForWoltDrive() {
        $Res = new ExtResponse();
        $Res->success = true;
        $EshopOrder = new EshopOrder();
        
        $id = @$_POST['orderId'];
        if (empty($id) || !is_numeric($id)) {
            $Res->success = false;
            $Res->message = array(
                'text' => __a(__FILE__, 'Wrong identifikator of order id'),
                'type' => 'error'
            );
            return $Res->toJson();
        }
        
        $data = $EshopOrder->findFirst(array(
            'conditions' => array(
                'id' => $id
            ),
        ));
        $orderLang = $EshopOrder->getLangByMerchantPid($data['merchant_pid']);
        App::setI18n($orderLang);
        
        // use delivery data if available, otherwise use billing data
        if (!empty($data['delivery_fullname'])) {
            $data['fullname'] = $data['delivery_fullname'];
        }
        if (!empty($data['delivery_street'])) {
            $data['street'] = $data['delivery_street'];
        }
        if (!empty($data['delivery_city'])) {
            $data['city'] = $data['delivery_city'];
        }
        if (!empty($data['delivery_zip'])) {
            $data['zip'] = $data['delivery_zip'];
        }
        if (!empty($data['delivery_country'])) {
            $data['country'] = $data['delivery_country'];
        }
        if (!empty($data['delivery_phone'])) {
            $data['phone'] = $data['delivery_phone'];
        }
        
        // normalize data
        if (!empty($data['fullname'])) {
            $fullnameParts = preg_split('/\s+/', $data['fullname']);
            $data['last_name'] = $data['first_name'] = array_pop($fullnameParts);
            if ($fullnameParts) {
                $data['first_name'] = implode(' ', $fullnameParts);
            }
        }

        $data['reference'] = $data['number'];

        if ($data['run_payment_methods_id'] == 2) {
            // jedna sa o dobierku
            if (!empty($data['order_price_suggested_taxless'])) {
                $data['dobierka'] = $data['order_price_suggested_taxless'] + $data['order_tax_suggested'];
            }
            else {
                $data['dobierka'] = $data['order_price_to_pay'];
            }
            $data['dobierka'] = number_format($data['dobierka'], 2, ',', '');
        }
        
        $data['description'] = __a(__FILE__, 'fragile');

        $Res->data = $data;
        
        if (!empty($data['shipment_ordered'])) {
            $Res->success = false;
            $Res->message = __v(__FILE__, 'Preprava pre túto objednávku už bola objednaná');
        }
        
        return $Res->toJson();
    }

    /**
     * Order WoltDrive shipping via its API
     * 
     * See API documentation: https://developer.wolt.com/docs/api/wolt-drive
     * 
     * Method is called from ExtJs via AJAX
     * Method sends data submitted from ExtJs form and returns answer from WoltDrive API.
     * Answer is showed in ExtJs form.
     */
    public function admin_orderWoltDrive() {
        $Res = new ExtResponse();
        $EshopOrder = new EshopOrder();

        $order = $EshopOrder->findFirst(array(
            'conditions' => array(
                'id' => $this->data['id'],
            ),
        ));

        if (!$order) {
            $Res->success = false;
            $Res->message = __v(__FILE__, 'Objednávka nebola nájdená');
            return $Res->toJson();
        }

        $merchantPid = $order['merchant_pid'];
        $originalLang = App::setI18n($EshopOrder->getLangByMerchantPid($merchantPid));

        try {
            // validation
            if (empty($this->data['phone'])) {
                $Res->success = false;
                $Res->message = __v(__FILE__, 'Nevyplnili ste telefónne číslo');
                App::setI18n($originalLang);
                return $Res->toJson();
            }

            if (!empty($order['shipment_ordered'])) {
                $Res->success = false;
                $Res->message = __v(__FILE__, 'Preprava pre túto objednávku už bola objednaná');
                App::setI18n($originalLang);
                return $Res->toJson();
            }

            App::loadModel('Eshop', 'EshopShipmentMethod');
            $ShipmentMethod = new EshopShipmentMethod();
            $shipmentMethod = $ShipmentMethod->findFirstBy('pid', 'woltDrive');
            if (
                !empty($shipmentMethod['zips'])
                && !in_array($this->data['zip'], Str::explode(',', $shipmentMethod['zips']))
            ) {
                $Res->success = false;
                $Res->message = __v(__FILE__, 'Tento typ dopravy nie je dostupný pre zvolenú adresu');
                App::setI18n($originalLang);
                return $Res->toJson();
            }

            $packagesCount = abs(intval(trim($this->data['package_count'])));
            $weights = Str::explode(' ', preg_replace('/\s+/', ' ', trim($this->data['weight'])));
            if ($packagesCount !== count($weights)) {
                throw new Exception(__v(
                    __FILE__,
                    'Zadajte rovnaký počet hmotností balíkov (oddelených medzerou) ako je počet balíkov'
                ));
            }
            foreach ($weights as &$weight) {
                $normalizedWeight = floatval(str_replace(',', '.', $weight));
                if (!$normalizedWeight) {
                    throw new Exception(__v(
                        __FILE__,
                        'Neplatná hodnota "%s" hmotnosti balíka',
                        $weight
                    ));
                }
                $weight = $normalizedWeight;
            }
            unset($weight);

            $note = '';
            if (!empty($this->data['comment'])) {
                $note .= Str::uppercaseFirst(trim($this->data['comment'], '. ')) . '. ';
            }
            if (!empty($this->data['description'])) {
                $note .= Str::uppercaseFirst(trim($this->data['description'], '. ')) . '. ';
            }
            $note = trim($note);

            //
            // send request to WoltDrive
            //
            if (ON_LOCALHOST || App::$slug === '_debug') {
                $venueId = '6878eef21f8b488c48d190a0';
                $apiUrl = "https://daas-public-api.development.dev.woltapi.com/v1/venues/{$venueId}/deliveries";
                $apiKey = 'L9JhYsPKfb5We_h9GEo5-HdcQZT-qjDLFyEkGvkNMTw';
            } 
            else {
                // $venueId = '68777795fad59f806c7d8509';
                // $apiUrl = "https://daas-public-api.wolt.com/v1/venues/{$venueId}/deliveries";
                // $apiKey = 'XVwYFaKLAEs2Hqaa4U_dAaKWWqSBItkxJ2xQaAhZP5Y';
            }

            // step 1: Shipment promise (required before delivery)
            $shipmentPromiseUrl = str_replace('/deliveries', '/shipment-promises', $apiUrl);
            $shipmentPromisePayload = array(
                'street' => $this->data['street'],
                'post_code' => $this->data['zip'],
                'min_preparation_time_minutes' => 30,
                'parcels' => array() // populated below
            );

            // populate parcels
            /*
            $products = $EshopOrder->getProductsDetails($order['id']);
            $isAlcoholOrder = !empty(array_filter($products, function ($p) {
                return !empty($p['alcohol']);
            }));
            */
            foreach ($weights as $weight) {
                $parcel = array(
                    'dimensions' => array(
                        'weight_gram' => $weight * 1000, // in grams
                    ),
                    'description' => $note,
                );
                /*
                if ($isAlcoholOrder) {
                    $parcel['dropoff_restrictions'] = array(
                        'age_limit' => 18,
                        'id_check_required' => true,
                    );
                    $parcel['tags'][] = 'alcohol';
                    $parcel['count'] = 1;
                }
                */
                $shipmentPromisePayload['parcels'][] = $parcel;
            }

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $shipmentPromiseUrl);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($shipmentPromisePayload));
            curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                'Content-Type: application/json',
                'Authorization: Bearer ' . $apiKey
            ));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

            $shipmentPromiseResponse = curl_exec($ch);
            $shipmentHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $curlError = curl_error($ch);
            curl_close($ch);

            if (
                $curlError 
                || $shipmentHttpCode !== 201
            ) {
                throw new Exception(__v(__FILE__, 'Invalid promise repsponse from WoltDrive API : %s', $shipmentPromiseResponse));
            }

            $shipmentPromiseResult = json_decode($shipmentPromiseResponse, true);
            $shipmentPromiseId = $shipmentPromiseResult['id'] ?? null;
            $dropoffLat = $shipmentPromiseResult['dropoff']['location']['coordinates']['lat'] ?? 0;
            $dropoffLon = $shipmentPromiseResult['dropoff']['location']['coordinates']['lon'] ?? 0;
            $deliveryPrice = $shipmentPromiseResult['price'];

            if (!$shipmentPromiseId) {
                throw new Exception(__v(__FILE__, 'Invalid repsponse from WoltDrive API for shipment promise'));
            }

            // step 2: Create delivery order
            $woltOrderData = array(
                'pickup' => array(
                    'options' => array(
                        'min_preparation_time_minutes' => 30,
                    ),
                ),
                'dropoff' => array(
                    'location' => array(
                        'coordinates' => array(
                            'lat' => $dropoffLat,
                            'lon' => $dropoffLon,
                        ),
                    ),
                ),
                'price' => $deliveryPrice,
                'recipient' => array(
                    'name' => $this->data['fullname'],
                    'phone_number' => $this->data['phone'],
                    'email' => $this->data['email'],
                ),
                'parcels' => $shipmentPromisePayload['parcels'],
                'shipment_promise_id' => $shipmentPromiseId,
                'customer_support' => array(
                    'phone' => App::getSetting('Core', 'phone'),
                    'email' => App::getSetting('Core', 'email.from'),
                ),
                'order_number' => $this->data['reference'],
            );

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $apiUrl);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($woltOrderData));
            curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                'Content-Type: application/json',
                'Authorization: Bearer ' . $apiKey
            ));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $curlError = curl_error($ch);
            curl_close($ch);

            if (
                $curlError 
                || $httpCode !== 201
            ) {
                throw new Exception(__v(__FILE__, 'Invalid response from WoltDrive API for delivery order: %s', $response));
            }

            $result = json_decode($response, true);

            $Res->message = __v(__FILE__, 'WoltDrive objednávka bola úspešne vytvorená. ID: %s', $result['wolt_order_reference_id'] ?? 'N/A');
            $Res->success = true;

            if ($Res->success) {
                $EshopOrder->save(array(
                    'id' => $this->data['id'],
                    'shipment_ordered' => date('Y-m-d H:i:s'),
                    'status' => 'enum_shipped_order',
                    'shipment_numbers' => $result['wolt_order_reference_id'] ?? null,
                    'delivery_tracking_url' => $result['tracking']['url'] ?? null,
                ));
            }

            //
            // send notification email
            //
            $msgBody = $this->getSetting('EshopOrder.msgBodyStatusChange.enum_shipped_order');
            if (empty($msgBody)) {
                $msgBody = $this->getSetting('EshopOrder.msgBodyStatusChange');
            }
            $msgSubject = $this->getSetting('EshopOrder.msgSubjectStatusChange.enum_shipped_order');
            if (empty($msgSubject)) {
                $msgSubject = $this->getSetting('EshopOrder.msgSubjectStatusChange');
            }
            $hasSuggestedPrices = false;
            $inserts = $EshopOrder->getInserts($this->data['id'], $hasSuggestedPrices);
            $shippedInfo = '<br><br>';
            $shippedInfo = '<br><br>';
            if ($this->data['packages_count'] > 1) {
                $shippedInfo .= __(__FILE__, 'Vaša objednávka bude doručovaná vo viac ako jednom balíku.') . ' ';
            }
            $inserts[':shippedInfo:'] = $shippedInfo . __(__FILE__, 'Prosím pozorne sledujte správy od dopravcu a nezabudnite si skontrolovať aj priečinky spam alebo nevyžiadaná pošta.');
            if (
                !empty($inserts[':userEmail:']) 
                && !ON_LOCALHOST
            ) {
                if ($EshopOrder->sendEmail(
                    $this->data['id'],
                    $msgBody,
                    $inserts[':userEmail:'],
                    array(
                        'subject' => $msgSubject,
                        'from' => $EshopOrder->getSenderEmail($order['merchant_pid']),
                        'cc' => $EshopOrder->getCopyEmail($order['merchant_pid']),
                        'inserts' => $inserts,
                        'embedImages' => true,
                    )
                )) {
                    $Res->message .= '<br>Objednávka prešla do stavu Odoslaná a klientovi bol odoslaný notifikačný email.';
                } else {
                    $Res->message .= '<br>Objednávka prešla do stavu Odoslaná, ale klientovi sa nepodarilo odoslať notifikačný email.';
                }
            }
        } 
        catch (Exception $e) {
            $Res->success = false;
            $Res->message = $e->getMessage();
            $errors[] = $e->getMessage();

            App::logError('WoltDrive order submition failure', array(
                'var' => $errors,
                'email' => true,
            ));

            return $Res->toJson();
        }
        
        App::setI18n($originalLang);
        
        return $Res->toJson();
    }
    
    /**
     * Order login. Standard login is not used as the order can be processed also 
     * without loging in as a 'quick order'.
     * 
     * @return html
     */
    public function login() {
        // if the quick order request is submitted then start quick order and redirect 
        // to checkout process
        if (!empty($this->data['start_quick_order'])) {
            $Order = $this->loadModel('EshopOrder');
            $Order = new EshopOrder();
            $Order->startQuickOrder();
            App::redirect(url(App::getContentLocator('Eshop.EshopOrders.checkout')));
        }
        return $this->loadView('eshopOrders/login', array(
            'allowQuickOrder' => $this->getSetting('EshopOrder.allowQuickOrder') 
        ));
    }
        
    public function checkout($stepSlug = null) {
        $this->loadModel('EshopOrder');
        $Order = new EshopOrder($stepSlug);
        $this->loadModel('EshopCart');
        $Cart = new EshopCart();
        
        // authenticate
        App::authenticate('Eshop', 'controlleraction', 'EshopOrders.checkout', array(
            'loginSlug' => App::getContentLocator('Eshop.EshopOrders.login'),
            'loginMessage' => __(__FILE__, 'Please login, register or continue without login'),
            'hasRights' => $Order->getCheckoutRights(),
        ));

        if ($this->getSetting('EshopOrders.checkout.allowOneStepCheckout')) {
            /*/>debug
            $oneStepCheckoutDebugLogData = array(
                'username' => App::getUser('username'),
                'cookies' => $_COOKIE
            );
            unset($oneStepCheckoutDebugLogData['cookies']['FAJNPHPSESSID']);
            App::startJsCapture();
            ?><script type="text/javascript">
                var oneStepCheckoutDebugLogData = <?php echo json_encode($oneStepCheckoutDebugLogData) ?>;
            </script><?php
            App::setJsFiles(array(
                '/app/modules/Eshop/js/oneStepCheckoutDebugLog.js',
            ));
            //*/
            if (!$Cart->countProducts()) {
                App::setMessage(__(__FILE__, 'Your cart is empty'));
                if (Eshop::hasB2BUser() || Eshop::hasDealerUser()) {
                    App::redirect(App::resolveUrl(App::getContentLocator('Eshop.EshopCarts.addMany')));
                }
                else {
                    App::redirect('/');
                }
            }
            $EshopGA4 = new EshopGA4DataLayer();
            $EshopGA4->addBeginCheckoutEvent($Cart->getProductsDetails());
            return App::loadReactApp('Eshop', 'EshopOrders/one-step-checkout');
        }

        // get checkout step
        $step = $Order->Checkout->getStep();
                
        //
        // process the submited data
        //
//        App::log('order', 'Orders::checkout()', array('var' => array(
//            'stepSlug' => $stepSlug,
//            'data' => $this->data,
//            'wizard' => App::getValue($_SESSION['_wizard']),
//        ))); //debug
        if ($this->data) {
            $valid = $Order->setCheckoutData($this->data);
            if (
                // if data are invalid then we must go back and display errors
                !$valid
                // if you submited data only to validate then return back to the step 
                // which has sent the data
                || !empty($this->data['_validate'])
            ) {
                if (!$valid) {
                    App::setMessage(__(__FILE__, 'Please complete or correct required data'));
                }
                App::redirect(url(array(
                    'locator' => SLUG, 
                    'args' => array($Order->Checkout->getPreviousSlug())
                )));
            }
            // if last step then redirect to order add page
            if ($step == 'checkoutSubmit') {
//                App::redirect('/objednavka/zhrnutie'); //debug
                App::redirect(url(App::getContentLocator('Eshop.EshopOrders.add')));
            }
        }
        
        //
        // Validate progress (do it only when data of previous step are processed)
        // 
        if (!$Order->Checkout->validateProgress()) {
            // if the progress is broken then redirect to first step of wizard
            App::clearMessages();
            App::setMessage(__(__FILE__, 'Checkout data has expired'));
            App::redirect(url(SLUG));
        }
             
        //
        // prepare view params
        //
        $wp = array();
        $wp['data'] = $Order->Checkout->getProperty('data');
        $wp['errors'] = $Order->Checkout->getProperty('errors');
        $wp['productCount'] = $Cart->countProducts();
        $wp['isQuickOrder'] = $Order->isQuickOrder();
        
        // load step specific data
        if ($step == 'checkoutStep01') {
            if (!$wp['isQuickOrder']) {
                $user = Eshop::getOrderUser();
                $User = App::loadModel('Core', 'User', true);
                $wp['UserProfile'] = $User->findFirst(array(
                    'fields' => array(
                        'User.first_name',
                        'User.last_name',
                        'User.email',
                        'UserProfile.salutation',
                        'UserProfile.degree',
                        'UserProfile.street',
                        'UserProfile.city',
                        'UserProfile.country',
                        'UserProfile.zip',
                        'UserProfile.phone',
                        'UserProfile.company_name',
                        'UserProfile.company_id_number',
                        'UserProfile.company_tax_number',
                        'UserProfile.company_vat_tax_number',
                        'UserProfile.person_type',
                        'UserProfile.another_delivery_address AS deliveryAddress',
                        'User.first_name AS delivery_first_name',
                        'User.last_name AS delivery_last_name',
                        'UserProfile.delivery_street',
                        'UserProfile.delivery_city',
                        'UserProfile.delivery_country',
                        'UserProfile.delivery_zip',
                        'UserProfile.fax',
                    ),
                    'conditions' => array('User.id' => $user['id']),
                    'joins' => array(
                        array(
                            'model' => 'UserProfile',
                            'type' => 'left',
                        )
                    ),
                ));
                if ($wp['UserProfile']['deliveryAddress']) {
                    $wp['UserProfile']['deliveryAddress'] = 'otherAddress';
                }
                else {
                    $wp['UserProfile']['deliveryAddress'] = 'customerAddress';
                }
                // this should not normally happen, only when DB is changed manually or
                // user person_type is changed in admin.
                if (array_key_exists('company_name', $wp) && empty($wp['company_name'])) {
                    unset($wp['company_name']);
                }
                $wp['data'] = array_merge($wp['UserProfile'], (array)$wp['data']);

                // validation of company_name before loading form to validate company_licence number and affidavit
                if (
                    !$Order->validate($wp['data'], array(
                        'alternative'=> 'checkoutStep01',
                        'allowFields' => 'company_name',
                    ))
                ) {
                    $wp['errors']['company_name'] = $Order->getErrors('company_name'); 
                }
            }
            $wp['data']['deliveryAddress'] = App::getValue($wp['data']['deliveryAddress'], 'customerAddress');
            
            $EshopGA4 = new EshopGA4DataLayer();
            $EshopGA4->addBeginCheckoutEvent($Cart->getProductsDetails());
        }
        elseif ($step == 'checkoutStep02') {
            $wp['Checkout'] = $Order->Checkout->getPropertyAllSteps('data');
            $cartProducts = $Cart->getProductsDetails(array('synchronize' => false));
            $cart = $Cart->getPrices($cartProducts);
            $Shipment = $this->loadModel('EshopShipmentMethod', true);
            // set conditions to retrieve shipment according to choosen delivery in previous step
            $deliveryAddress = $wp['Checkout']['checkoutStep01']['deliveryAddress'];
            $abroadDelivery = $Order->hasAbroadDelivery();
            $shipmentConditions = array('EshopShipmentMethod.active' => true);
            if ($deliveryAddress == 'merchantAddress') {
                $shipmentConditions['EshopShipmentMethod.pid'] = 'pickup';
                if (Eshop::hasB2BUser()) {
                    $shipmentConditions['EshopShipmentMethod.business_type'] = array('b2b', 'any');
                }
                else {
                    $shipmentConditions['EshopShipmentMethod.business_type'] = array('b2c', 'any');
                }
            }
            elseif ($abroadDelivery) {
                $shipmentConditions['EshopShipmentMethod.pid'] = 'abroadDelivery';
            }
            else {
                $shipmentConditions[] = array(
                    'EshopShipmentMethod.pid' => null,
                    'OR',
                    array(
//                        'EshopShipmentMethod.pid !=' => 'pickup',
                        'EshopShipmentMethod.pid !=' => 'abroadDelivery'
                    )
                );
                if (Eshop::hasB2BUser()) {
                    $shipmentConditions['EshopShipmentMethod.business_type'] = array('b2b', 'any');
                }
                else {
                    $shipmentConditions['EshopShipmentMethod.business_type'] = array('b2c', 'any');
                }
            }
            $shipmentConditions['EshopShipmentMethod.lang'] = App::$lang;
            $wp['EshopShipmentMethod'] = $Shipment->findList(array(
                'key' => 'EshopShipmentMethod.id',
                'fields' => array(                    
                    'EshopShipmentMethod.id',
                    'EshopShipmentMethod.pid',
                    'EshopShipmentMethod.name',
                    'EshopShipmentMethod.description',
                    'EshopShipmentMethod.info',
                    'EshopShipmentMethod.price',
                    'EshopShipmentMethod.products_total_price_alternatives',
                    'EshopShipmentMethod.package_weight_price_alternatives',
                    'EshopShipmentMethod.delivery_time',
                    'EshopShipmentMethod.info_locator',
                    'EshopShipmentMethod.tracking_url',
                ),
                'conditions' => $shipmentConditions,
                'order' => 'EshopShipmentMethod.sort ASC',
            ));
            $shipmentIds = array();
            $shipmentCount = count($wp['EshopShipmentMethod']);
            $wp['unappliedVolumeLimits'] = $Cart->getUnappliedVolumeLimits();
            foreach($wp['EshopShipmentMethod'] as $i => &$shipment) {
                // get shipmentIds to retrieve only asociated payment methods
                $shipmentIds[] = $shipment['id'];
                // if there is just one shipment method or if the pickup was 
                // selected in the first step then select shipment implicitly
                if (
                    $shipmentCount == 1
                    || (                                
                        $wp['Checkout']['checkoutStep01']['deliveryAddress'] == 'merchantAddress'
                        && $shipment['pid'] == 'pickup'
                    )
                ) {
                    // if shipment was already set and it differs now then reset payment
                    if (
                        isset($wp['data']['shipment'])
                        &&$wp['data']['shipment'] != $shipment['id']
                    ) {
                        unset($wp['data']['payment']);
                    }
                    $wp['data']['shipment'] = $shipment['id'];
                }
                // update shipment price
                $shipment = $Shipment->getPrices($shipment, array(
                    'productsPrices' => $cart,
                    'forceNoPrices' => !empty($wp['unappliedVolumeLimits']),
                ));
                // prepare dpd pickup places
                $shipment['pickup_places'] = array();
                if (
                    $shipment['pid'] == 'dpdPickup'
                    && !($shipment['pickup_places_list'] = $Order->getPickupPlacesList('dpdPickup', array(
                        'pickupPlaces' => &$shipment['pickup_places'],
                        'sort' => true,
                    )))
                ) {
                    unset($wp['EshopShipmentMethod'][$i]);
                }
            }
            unset($shipment); // unset reference
            if ($wp['unappliedVolumeLimits']) {
                $wp['volumeLimitMessage'] = $Cart->getVolumeLimitMessage(
                    $Order->getDeliveryCountry()
                );
            }
            $Payment = App::loadModel('Payment', 'PaymentMethod', true);
            $wp['PaymentMethod'] = $Payment->findList(array(
                'key' => 'PaymentMethod.id',
                'fields' => array(                    
                    'PaymentMethod.id',
                    'PaymentMethod.pid',
                    'PaymentMethod.name',
                    'PaymentMethod.description',
                    'PaymentMethod.info',
                    'PaymentMethod.price',
                    'PaymentMethod.products_total_price_alternatives',
                    'PaymentMethod.info_locator',
                ),
                'conditions' => array(
                    'PaymentMethod.active' => true,
                    'EshopShipmentPaymentMethod.run_eshop_shipment_methods_id' => $shipmentIds, 
//                    'PaymentMethod.pid !=' => (isset($_GET['tp'])) ? '' : 'tatrapay', //debug
                ),
                'joins' => array(
                    array(
                        'module' => 'Eshop',
                        'model' => 'EshopShipmentPaymentMethod',
                        'type' => 'left',
                    )
                ),
                'order' => 'EshopShipmentPaymentMethod.id ASC',
                'group' => array('PaymentMethod.id'),
            ));
            foreach($wp['PaymentMethod'] as &$payment) {
                $payment = $Payment->getPrices($payment, array(
                    'productsPrices' => $cart,
                ));
            }
            // if there is just one payment then select it implicitly
            if (count($wp['PaymentMethod']) == 1) {
                $wp['data']['payment'] = $payment['id'];
            }
            unset($payment); // unset reference
            $ShipmentPayment = $this->loadModel('EshopShipmentPaymentMethod', true);
            $wp['EshopShipmentPaymentMethod'] = $ShipmentPayment->findList(array(
                'key' => 'run_eshop_shipment_methods_id',
                'fields' => array('run_payment_methods_id'),
                'conditions' => array('run_eshop_shipment_methods_id' => $shipmentIds),
                'accumulate' => true,
            ));
            // if there is a limit of order total for COD use then remove all pairs
            // {shipmentId} => {paymentId} where this condition is not met (for actual 
            // shipment and payment prices set here above)
            $codOrderPriceLimit = $this->getSetting('EshopOrder.codOrderPriceLimit');
            if ($codOrderPriceLimit) {
                $this->loadModel('EshopCart');
                $Cart = new EshopCart();
                $cartTotals = $Cart->getPrices($Cart->getProductsDetails());
                $codPaymentId = null;
                $hasCodPayment = false;
                foreach ($wp['EshopShipmentPaymentMethod'] as $shipmentId => &$paymentIds) {
                    if (!isset($wp['EshopShipmentMethod'][$shipmentId])) {
                        continue;
                    }
                    $shipment = $wp['EshopShipmentMethod'][$shipmentId];
                    foreach ($paymentIds as $paymentIdIndex => &$paymentId) {
                        if (!isset($wp['PaymentMethod'][$paymentId])) {
                            continue;
                        }
                        $payment = $wp['PaymentMethod'][$paymentId];
                        if ($payment['pid'] === 'courier') {  //cod
                            $codPaymentId = $paymentId;
                            $orderTotal = $cartTotals['products_price_actual_taxed']
                                + $shipment['shipment_price_actual_taxed']
                                + $payment['payment_price_actual_taxed'];
                            if ($orderTotal > $codOrderPriceLimit) {
                                unset($paymentIds[$paymentIdIndex]);
                            }
                            else {
                                $hasCodPayment = true;
                            }
                        }
                    }
                    unset($paymentId);
                    // reindex because of correct render in view
                    $paymentIds = array_values($paymentIds);
                    // remove shipment if there are no more payments
                    if (empty($paymentIds)) {
                        unset($wp['EshopShipmentMethod'][$shipmentId]);
                    }
                }
                unset($paymentIds);
                // remove COD payment if any and not used at all
                if (
                    $codPaymentId !== null
                    && !$hasCodPayment
                ) {
                    unset($wp['PaymentMethod'][$codPaymentId]);
                }
            }
        }
        elseif ($step == 'checkoutStep03') {
            // get cart products details
            $wp['EshopCartProduct'] = $Cart->getProductsDetails(array(
                'getSpecialOffers' => true,
                'synchronize' => true,
                'getDisponibilityLabel' => true,
            ));
            $Cart->setAdjustedAndWarningProductsAppMessages();
            if (!$wp['EshopCartProduct']) {
                App::setMessage(__(__FILE__, 'Your cart is empty'));
                App::redirect('/');
            }
            // get totals
            $wp['EshopCart'] = $Cart->getPrices($wp['EshopCartProduct']);
            $wp['Checkout'] = $Order->getNewOrderDeliveryData(
                $Order->Checkout->getPropertyAllSteps('data', true)
            );
            // get user info
            if (!$Order->isQuickOrder()) {
                $user = Eshop::getOrderUser();
                $User = App::loadModel('Core', 'User', true);
                $userProfile = $User->findFirst(array(
                    'fields' => array(
                        'User.first_name',
                        'User.last_name',
                        'User.email',
                        'UserProfile.salutation',
                        'UserProfile.degree',
                        'UserProfile.street',
                        'UserProfile.city',
                        'UserProfile.country',
                        'UserProfile.zip',
                        'UserProfile.phone',
                        'UserProfile.fax',
                        'UserProfile.reference_from',
                        'UserProfile.newsletters_agreement',
                    ),
                    'conditions' => array('User.id' => $user['id']),
                    'joins' => array(
                        array(
                            'model' => 'UserProfile',
                            'type' => 'left',
                        )
                    ),
                ));
                $wp['UserProfile'] = $userProfile;
                $wp['Checkout'] = array_merge(
                    $userProfile,
                    $wp['Checkout']
                );
                // set the email explicitly
                $wp['Checkout']['email'] = $userProfile['email'];
            }
            
            // get shipment name & price
            $wp['unappliedVolumeLimits'] = $Cart->getUnappliedVolumeLimits();
            $Shipment = $this->loadModel('EshopShipmentMethod', true);
            $shipment = $Shipment->findFirst(array(
                'fields' => array(
                    'EshopShipmentMethod.pid', 
                    'EshopShipmentMethod.name', 
                    'EshopShipmentMethod.price', 
                    'EshopShipmentMethod.products_total_price_alternatives', 
                    'EshopShipmentMethod.package_weight_price_alternatives'
                ),
                'conditions' => array(
                    'EshopShipmentMethod.id' => $wp['Checkout']['shipment'],
                    'lang' => App::$lang
                ),
            ));
            $shipment = $Shipment->getPrices($shipment, array(
                'productsPrices' => $wp['EshopCart'],
                'forceNoPrices' => !empty($wp['unappliedVolumeLimits']),
            ));
            $wp['EshopShipmentMethod'] = $shipment;
            $wp['abroadDelivery'] = $Order->hasAbroadDelivery();
            // get payment name & price
            $Payment = App::loadModel('Payment', 'PaymentMethod', true);
            $payment = $Payment->findFirst(array(
                'fields' => array(
                    'PaymentMethod.name', 
                    'PaymentMethod.price',
                    'PaymentMethod.products_total_price_alternatives',
                    'PaymentMethod.online',
                ),
                'conditions' => array(
                    'PaymentMethod.id' => $wp['Checkout']['payment'],
                ),
            ));
            $payment = $Payment->getPrices($payment, array(
                'productsPrices' => $wp['EshopCart'],
            ));
            $wp['PaymentMethod'] = $payment;
            // get order prices
            $wp['EshopOrder'] = $Order->getPrices(array(
                'productsPrices' => $wp['EshopCart'],
                'shipmentPrices' => $wp['EshopShipmentMethod'],
                'paymentPrices' => $wp['PaymentMethod'],
                'recyclingFeePrices' => $Order->getRecyclingFeePrices(array(
                    'products' => $wp['EshopCartProduct'],
                    'order' => $Order->Checkout->getPropertyAllSteps('data', true),
                )),
            ));
            // get urls
            $stepSlugs = $Order->Checkout->getSlugs();
            $wp['urlStep01'] = url(array(
                'locator' => SLUG,
                'args' => array($stepSlugs['checkoutStep01']),
            ));
            $wp['urlStep02'] = url(array(
                'locator' => SLUG,
                'args' => array($stepSlugs['checkoutStep02']),
            ));
            $wp['urlTermsAndConditions'] = App::getContentUrl('Eshop.termsAndConditions');
            $wp['slugProductView'] = App::getContentLocator('Eshop.EshopProducts.view');
            // get list of possible advances
            $wp['advances'] = $Order->getAdvances($wp['EshopOrder']);
            
            $EshopGA4 = new EshopGA4DataLayer();
            $EshopGA4->addAddShippingInfoEvent($wp['EshopCartProduct'], array(
                'shippingTier' => $shipment['name'],
            ));
            $EshopGA4->addAddPaymentInfoEvent($wp['EshopCartProduct'], array(
                'paymentType' => $payment['name'],
            ));
        } //END elseif ($step == 'checkoutStep03')
        
        // prepare urls for checkout steps navigation
        $wp['urlCartView'] = App::getContentUrl('Eshop.EshopCarts.view');
        $slugs = $Order->Checkout->getSlugs();
        $wp['urlCheckoutStep01'] = url(array(
            'locator' => SLUG, 
            'args' => array($slugs['checkoutStep01'])
        ));
        $wp['urlCheckoutStep02'] = url(array(
            'locator' => SLUG, 
            'args' => array($slugs['checkoutStep02'])
        ));
        // prepare urls for previous and next steps
        if (($previousSlug = $Order->Checkout->getPreviousSlug())) {
            $wp['urlPrevious'] = url(array(
                'locator' => SLUG,
                'args' => array($previousSlug),
            ));
        }
        else {
            // if there is no previous step, go to cart
            $wp['urlPrevious'] = App::getContentUrl('Eshop.EshopCarts.view');
        }
        if (($nextSlug = $Order->Checkout->getNextSlug())) {
            $wp['urlNext'] = url(array(
                'locator' => SLUG,
                'args' => array($nextSlug),
            ));
        }
        else {
            // it should not happen that there is no next step 
            // (the process is redirected on last valid data submit to EshopOrder::add())
            App::setMessage(__(__FILE__, 'Invalid state of order checkout. Please, contact us.'));
            App::logError('Invalid state of order checkout', array(
                'var' => array('session' => $_SESSION),
                'email' => true,
            ));
            App::redirect(App::getRefererUrl('/'));
        }
        
        // google remarketing data
        App::setGlobal('Core', 'googleRemarketingParams', array(
            'pagetype' => 'conversionintent',
        ));
        
        return $this->loadView('eshopOrders/' . $step, $wp);
    }
        
    /**
     * Adds new order and returns it number on success.
     * This should be called as snippet on last page of order process, e.g.:
     * 
     * Your order number&nbsp;<object _snippet="Eshop.EshopOrders.add" _snippet_generic="1"></object>&nbsp;has been successfully submited.
     * 
     * @return string New order number
     */
    public function add() {
        $Order = $this->loadModel('EshopOrder', true);
        
        // authenticate
        App::authenticate('Eshop', 'controlleraction', 'EshopOrders.checkout', array(
            'loginSlug' => App::getContentLocator('Eshop.EshopOrders.login'),
            'loginMessage' => __(__FILE__, 'Please login, register or continue without login'),
            'hasRights' => $Order->getCheckoutRights(),
        ));
        
        try {
            $orderAddResult = $Order->add();
        }
        catch (Exception_App_ProcessingReservationFailure $e) {
            App::setMessage(__(__FILE__, 'Objednávku sa nepodarilo spracovať kvôli vyťaženosti systému. Skúste ju odoslať znovu.'));
            App::logInfo(__(__FILE__, 'Objednávku sa nepodarilo spracovať kvôli vyťaženosti systému. Skúste ju odoslať znovu.'), array(
                'email' => true,
            ));
            App::redirect(App::resolveUrl(App::getContentLocator('Eshop.EshopOrders.checkout')));
        }
        // missing checkout or cart data
        if ($orderAddResult === null) {
            if (!($newOrderId = $Order->getNewOrderId())) {
                App::setMessage(__(__FILE__, 'Your order has either expired or has been already submited'));
                App::redirect('/');
            }
            // if there is still new order id stored in session so this is 
            // just refreshed order add page - display it
            $order = $Order->findFirstBy('id', $newOrderId, array('fields' => 'number'));
            return $order['number'];
        }
        // checkout data validation error
        elseif ($orderAddResult === false) {
            $OrderProduct = $this->loadModel('EshopOrderProduct', true);
            if (($errors = $Order->getErrors())) {
                if (!empty($errors['_processing'])) {
                    App::setMessage(__(__FILE__, 'Internal error has occured. Please, contact us.'));
                }
                else {
                    App::setMessage(array_shift(array_shift($errors)));
                }
                App::logError('Invalid new order data', array(
                    'var' => array(
                        'EshopOrderErrors' => $errors,
                        'session' => $_SESSION,
                    ),
                    'email' => true,

                ));
            }
            elseif (($errors = $OrderProduct->getErrors())) {
                App::setMessage(array_shift(array_shift($errors)));
                App::logError('Invalid new order product data', array(
                    'var' => array( 
                        'EshopOrderProductErrors' => $errors,
                        'session' => $_SESSION,
                    ),
                    'email' => true,
                ));
            }
            else {
                App::setMessage(__(__FILE__, 'Invalid new order data. Please, contact us.'));
                App::logError('Invalid new order data', array(
                    'var' => array(
                        'session' => $_SESSION
                    ),
                    'email' => true,
                ));
            }
            App::redirect(App::getRefererUrl('/'));
        }
        // adjusted products
        elseif (is_array($orderAddResult)) {
            $this->loadModel('EshopCart');
            $Cart = new EshopCart();
            $Cart->setAdjustedAndWarningProductsAppMessages(array_merge($orderAddResult, array(
                'on' => 'orderAdd',
            )));
            App::redirect(App::getRefererUrl(App::resolveUrl(App::getContentLocator('Eshop.EshopCarts.view'))));
        }
        // ok, new order number returned 
        else {
            // send message to client
            if (!$Order->sendNewOrderEmail($Order->getId())) {
                App::setMessage(__(__FILE__, 'New order message send has failed'));
                App::logError('New order message send has failed', array(
                    'email' => true
                ));
            }
            $Order->setGoogleRemarketingData($Order->getId());
            $EshopGA4 = new EshopGA4DataLayer();
            $EshopGA4->addPurchaseEvent($Order->getId());
            return $orderAddResult . 
                $Order->getGoogleEcommerceTrackingCode($Order->getId()) .
                $Order->sendVerifiedByClientsRequest($Order->getId()) .
                $Order->getHeurekaConversionCode($Order->getId());
        }
    }
    
    /**
     * @param int $orderId
     * @param string $orderToken
     */
    public function confirm($orderId = null, $orderToken = null) {
        $this->loadModel('EshopOrder');
        $Order = new EshopOrder();
        $failureMessage = null;
        try {
            $orderNumber = $Order->confirm($orderId, $orderToken);
        }
        catch (Throwable $e) {
            $failureMessage = $e->getMessage();
        }
        return $this->loadView('eshopOrders/confirm', array(
            'orderNumber' => $orderNumber,
            'failureMessage' => $failureMessage,
        ));
    }
    
    /**
     * @param int $orderId
     * @param string $orderToken
     */
    public function cancel($orderId = null, $orderToken = null) {
        $this->loadModel('EshopOrder');
        $Order = new EshopOrder();
        $failureMessage = null;
        if ($this->data) {
            try {
                $orderNumber = $Order->cancel($orderId, $orderToken);
            }
            catch (Throwable $e) {
                $failureMessage = $e->getMessage();
            }
        }
        elseif (
            !($orderNumber = $Order->findField('number', array(
                'conditions' => array(
                    'id' => $orderId,
                    'token' => $orderToken,
                ),
            )))
        ) {
            $failureMessage = __(
                __FILE__,
                'Neexistujúca objednávka.'
            );
        }
        return $this->loadView('eshopOrders/cancel', array(
            'data' => $this->data,
            'orderNumber' => $orderNumber,
            'failureMessage' => $failureMessage,
        ));
    }
    
    /**
     * Gets new order payment link to be used as info on last page of order process:
     * 
     * Your order number&nbsp;<object _snippet="Eshop.EshopOrders.add" _snippet_generic="1"></object>&nbsp;has been successfully submited.
     * <object _snippet="Eshop.EshopOrders.getUnconfirmedOrderInfo" _snippet_generic="1"></object>
     * <object _snippet="Eshop.EshopOrders.getNewOrderPaymentLink" _snippet_generic="1"></object>
     * 
     * @return string New order payment link. The link is returned together with 
     *      corresponding text, because it is displayed only in case that the chosen
     *      payment method of order is online
     */
    public function getUnconfirmedOrderInfo() {
        $this->loadModel('EshopOrder');
        $Order = new EshopOrder();
        if (!($newOrderId = $Order->getNewOrderId())) {
            return false;
        }
        return $Order->getUnconfirmedOrderInfo($newOrderId);
    }
    
    /**
     * Gets new order payment link to be used as info on last page of order process:
     * 
     * Your order number&nbsp;<object _snippet="Eshop.EshopOrders.add" _snippet_generic="1"></object>&nbsp;has been successfully submited.
     * <object _snippet="Eshop.EshopOrders.getNewOrderPaymentLink" _snippet_generic="1"></object>
     * 
     * @return string New order payment link. The link is returned together with 
     *      corresponding text, because it is displayed only in case that the chosen
     *      payment method of order is online
     */
    public function getNewOrderPaymentLink() {
        $this->loadModel('EshopOrder');
        $Order = new EshopOrder();
        if (!($newOrderId = $Order->getNewOrderId())) {
            return false;
        }
        return $Order->getPaymentLink($newOrderId);
    }
    
    /**
     * Gets new order email to be used as info on last page of order process:
     * 
     * Your order number&nbsp;<object _snippet="Eshop.EshopOrders.add" _snippet_generic="1"></object>&nbsp;has been successfully submited.
     * En email message has been send to <object _snippet="Eshop.EshopOrders.getNewOrderEmail" _snippet_generic="1"></object> address.
     * 
     * @return string New order email
     */
    public function getNewOrderEmail() {
        $this->loadModel('EshopOrder');
        $Order = new EshopOrder();
        if (!($newOrderId = $Order->getNewOrderId())) {
            return false;
        }
        $inserts = $Order->getInserts($newOrderId);
        return $inserts[':userEmail:'];
    }
    
    /**
     * Make the payment.
     * 
     * @param int $orderId Order id
     * @param int $orderToken Order token to securize public payment requests in case of 
     *      quick orders
     */
    public function pay($orderId, $orderToken) {
        $Order = $this->loadModel('EshopOrder', true);
        try {                
            $Order->pay($orderId, $orderToken, '/mvc/Eshop/EshopOrders/processPaymentResponse/');
            // if no exception is raised then this poins is not reached 
            // Application redirects to payment URL
        }
        catch (Throwable $e) {  
            App::setMessage($e->getMessage(), 'error');
            App::redirect('/');
        }
    }
    
    /**
     * Checks if Order is paid. Used with QR payment methods like Viamo.
     * 
     * @param int $orderNumber Order number
     * @param int $orderToken Order token to securize public payment checks
     */
    public function isPaid($orderNumber, $orderToken) {
        App::setLayout(false);
        $Res = new stdClass();
        $this->loadModel('EshopOrder');
        $Order = new EshopOrder();
        
        $order = $Order->findFirstBy('number', $orderNumber,
                array('fields' => array(
                    'payment_status',
                    'token',
                    ))
                );

        if(
            $order['token'] == $orderToken 
            && $order['payment_status'] == 'enum_payment_paid'
        ) {
            $Res->success = true;
            $Res->message = array(
                'text' => __(__FILE__, 'Order was succesfully paid.'),
                'type' => 'info',
            );
        } else {
            $Res->success = false;
        }

        return json_encode($Res);
    }
    
    /**
     * Processes the payment processing steps: validation, confirmation, rejection.
     * 
     * @param string $step Payment processing step name. Possible values are 'validation', 'confirmation', 'rejection'.
     * 
     * @return string|bool Processing result string to be sent to bank. If an error
     *      has occured then FALSE. It should be just returned (echoed), even the FALSE, 
     *      as answer to request from bank. Do not forget to turn off the layout.
     */
    public function processPayment($step = null) {
        $Order = $this->loadModel('EshopOrder', true);
        App::setLayout(false);
        App::setDebug(false);
        try {
            return $Order->processPayment($step);
        }
        catch (Throwable $e) {
            return FALSE;
        }
    }
    
    /**
     * Process the payment final response
     */
    public function processPaymentResponse() {
        $Order = $this->loadModel('EshopOrder', true);
        try {
            $resultStatus = $Order->processPaymentResponse();
        }
        catch (Throwable $e) {
            App::setMessage($e->getMessage(), 'error');
            App::redirect('/');
        }
        // give the answer to the customer
        switch ($resultStatus) {
            case 'enum_payment_paid':
                App::setMessage(
                    __(__FILE__, 'Your payment was accepted.'), 
                    'success'
                );
                if (!$Order->sendPaymentSuccessfulEmail($Order->getId())) {
                    App::logError('Order successful payment message send has failed', array(
                        'email' => true
                    ));
                }
                break;
                
            case 'enum_payment_tout':
                App::setMessage(
                    __(__FILE__, 'Your bank did not authorize the payment or the bank service is temporarily busy. Please contact our customer service if you do not receive confirmation about the payment being accepted within 24 hours.'), 
                    'info'
                );
                break;
            
            case 'enum_payment_failed':
            default:
                App::setMessage(
                    __(__FILE__, 'We are sorry. Your payment was not authorized by your bank.'), 
                    'error'
                );
                if (!$Order->sendPaymentFailedEmail($Order->getId())) {
                    App::logError('Order failed payment message send has failed', array(
                        'email' => true
                    ));
                }
                break;
        }
        App::redirect('/');
    }  
    
    /**
     * Checks all unpaid orders with payment methods of Tatrabanka and if TB answers
     * that order is paid then its payment status is updated (otherwise nothing changes).
     * 
     * @return array Progress array containing amounts of processed orders
     */
    public function updateTatrabankaPaymentsStatuses() {
        App::setLayout(false);
        $this->loadModel('EshopOrder');
        $Order = new EshopOrder();
        $result = $Order->updateTatrabankaPaymentsStatuses();
        return '<pre>' . print_r($result, true) . '</pre>';
    }
    
    /**
     * Checks payment status of specified order in Tatrabanka and returns json encoded
     * answer.
     * 
     * ATTENTION: This method should be used by AJAX
     * 
     * @param int $id Order id
     * 
     * @return string Json encoded array containing items 'paymentStatus' and 'message'.
     */
    public function admin_checkTatrabankaPaymentStatus($id = null) {
        $response = array(
            'paymentStatus' => null,
            'message' => null,
        );
        try {            
            $this->loadModel('EshopOrder');
            $Order = new EshopOrder();
            $result = $Order->checkTatrabankaPaymentStatus($id);
            $response['paymentStatus'] = $result;
            if (empty($result)) {
                $response['message'] = __a(__FILE__, 'Objednávka nie je hradená platobnou metódou Tatrabanky');
            }
            elseif ($result === 'enum_payment_paid') {
                $response['message'] = __a(__FILE__, 'Tatrabanka eviduje úspešnú platbu objednávky');
            }
            elseif ($result === 'enum_payment_tout') {
                $response['message'] = __a(__FILE__, 'Tatrabanka spracováva platbu objednávky. Výsledok spracovanie skontrolujte neskôr.');
            }
            elseif ($result === 'enum_payment_failed') {
                $response['message'] = __a(__FILE__, 'Tatrabanka neeviduje platbu objednávky');
            }
            elseif ($result === 'enum_payment_none') {
                $response['message'] = __a(__FILE__, 'Tatrabanka neeviduje platbu objednávky');
            }
        } 
        catch (Throwable $ex) {
            $response['message'] = $ex->getMessage();
        }
        return json_encode($response);        
    }
    
    /**
     * Generate xml file(eshop order) for system Pohoda.
     */
    public function admin_generatePohodaXmlOrder() {
        $Res = new ExtResponse();
        $Res->success = true;
        $EshopOrder = new EshopOrder();
        
        $id = @$_POST['orderId'];
        if (empty($id) || !is_numeric($id)) {
            $Res->success = false;
            $Res->message = array(
                'text' => __a(__FILE__, 'Wrong identifikator of order id'),
                'type' => 'error'
            );
            return $Res->toJson();
        }
        if($EshopOrder->generatePohodaXmlOrder($id)){
            $Res->success = true;
            $Res->message = array(
                'text' => __a(__FILE__, 'Pohoda xml order was successfully created'),
                'type' => 'info'
            );
        } else {
            $Res->success = false;
            $Res->message = array(
                'text' => array_shift($EshopOrder->getErrors('_processing')),
                'type' => 'error'
            );
        }
        
        return $Res->toJson();
    }
    
    /**
     * Generate csv file for specified order
     */
    public function admin_exportOrderCsv($orderId) {
        try {
            $Export = $this->loadModel('EshopExport', true);
            $file = $Export->exportOrderCsv($orderId);
            echo "<a href =\"$file\" target=\"_blank\">$file</a>";
        }
        catch (Throwable $e) {
            echo $e->getMessage();
//            App::logError('Generation of order CSV has failed', array(
//                'var' => $e,
//                //'email' => true,
//            ));
        }
    }

    /**
     * Exports orders of BindasGroup for previous 2 months and the elapsed part of actual month
     */
    public function admin_export() {
        $lang = !empty($_REQUEST['lang']) ? $_REQUEST['lang'] : App::$lang;
        
        // get date of the firts day of the month before the last)
        $Date = new DateTime(); 
        $Date->setDate($Date->format('Y'), $Date->format('m'), 1); 
        $Date->modify('-2 months'); 
        $date= $Date->format('Y-m-d');
        
        $Order = new EshopOrder();
        $orders = $Order->find(array(
            'joins' => array(
                array(
                    'type' => 'left',
                    'module' => 'Core',
                    'model' => 'User',
                ),
                array(
                    'type' => 'left',
                    'module' => 'Core',
                    'model' => 'UserProfile',
                    'toModule' => 'Core',
                    'toModel' => 'User',
                ),
            ),
            'conditions' => array(
                'UserProfile.run_eshop_sales_groups_id' => 1, //HARDCODED (id of BindasGroup)
                'EshopOrder.merchant_pid' => $Order->getMerchantPidByLang($lang),
                //'EshopOrder.business_type' => 'b2b', // this should be fullfilled naturaly
                'EshopOrder.created >=' => $date,
            ),
            'fields' => array(
                'EshopOrder.created AS `Dátum objednávky`',
                'EshopOrder.number AS `Číslo objednávky`',
                'EshopOrder.payment_status AS `Zaplatené`',
                'EshopOrder.order_price_actual_taxless AS `Cena bez DPH`',
                'EshopOrder.order_price_actual_taxless + EshopOrder.order_tax_actual AS `Cena s DPH`',
                'EshopOrder.products_price_actual_taxless AS `Cena produktov bez DPH`',
                'EshopOrder.products_price_actual_taxless + EshopOrder.products_tax_actual AS `Cena produktov s DPH`',
                //'EshopOrder.company_name AS `Názov spoločnosti`',
                'EshopOrder.fullname AS `Meno/Názov`',
                'EshopOrder.street AS `Ulica`',
                'EshopOrder.city AS `Mesto`',
                'EshopOrder.zip AS `PSČ`',
                'EshopOrder.company_id_number AS `IČO`',
                'EshopOrder.company_alcohol_licence_number AS `Číslo povolenia na distribúciu a predaj alkoholu`',
                'EshopOrder.email AS `E-mail`',
                'EshopOrder.phone AS `Telefón`',
            ),
            'order' => array(
                'EshopOrder.created DESC',  
            ),
        ));
        
        foreach($orders as &$order) {
            //$order['Dátum objednávky'] = Utility::formatDate($order['Dátum objednávky'], 'j.n.Y');
            $order['Zaplatené'] = __(__FILE__, $order['Zaplatené']);
        }
        unset($order);
        
        $Order->export($orders, array(
            'file' => 'bindas-group-export-' . date('Ymd-His'),
            'format' => 'xlsx',
        ));
    }

    
    /**
     * Adds new order created by external source
     * 
     * ATTENTION: This method is exposed by webservice and so should not
     * use $this->data and $this->params
     * 
     * @param string $merchant Merchant name
     * @param string $order Json encoded order data hashed by client key
     * 
     * 
     * @return string New order number
     */
    public function addExternal($merchant, $order) {
        // available merchants
        $availableMerchants = array(
            'rumovabanka' => array(
                'pid' => 'rumovabanka',
                'key' => '3NDNmIpjnx3mrs03VHGj',
            )
        );
        if (empty($availableMerchants[$merchant])) {
            return 0;
        }
        $merchantPid = $availableMerchants[$merchant]['pid'];
        $merchantKey = $availableMerchants[$merchant]['key'];
        // decrypt order data and validate request (according the result of decryption)
        $order = json_decode(Utility::decrypt($order, $merchantKey), true);
        if (
            empty($order) 
            || !is_array($order)
        ) {
            return 0;
        }
        // make the new order processing
        $Order = $this->loadModel('EshopOrder', true);
        $result = $Order->addExternal($merchantPid, $order);
        if (empty($result)) {
            $result = '0 - ' . json_encode($Order->getErrors());
        }
        return $result;
    }

    /**
     * CRON (each 5 minutes)
     * 
     * Imports new orders from Baselinker
     */
    public function addFromBaselinker() {
        App::setLayout(false);
        $this->loadModel('EshopOrder');
        $Order = new EshopOrder();
        $result = $Order->addFromBaselinker();
        return echoReadable($result, array('return' => true, 'json' => true));
    }
    
    /**
     * CRON or WEBHOOK (set on Mall side, 
     * see https://knowledgebase.mallgroup.com/webhook-pro-stavy-objednavek/ )
     * 
     * Imports new orders from Mall
     */
    public function importFromMall() {
        $this->loadModel('EshopOrder');
        $Order = new EshopOrder();
        $Order->importFromMall();
    }
    
    /**
     * Sets specified user as "order user" and redirects back.
     * If no $userId is provided then selectbox of users is returned.
     * 
     * @param int $userId Optional.
     * 
     * @return string Selectbox of users is returned but only if no $userId is provided
     */
    public function setUser($userId = null) {
        if ($userId) {
            if ($userId === 'default') {
                $userId = App::getUser('id');
            }
            Eshop::setOrderUser($userId);
            // reset checkout data on each user change
            $this->loadModel('EshopOrder');
            $Order = new EshopOrder();
            $Order->clearCheckout();
            App::redirect(App::getRefererUrl(App::resolveUrl(App::getContentLocator('Eshop.EshopCarts.addMany'))));
        }
        // display selectbox
        $orderUserId = Eshop::getOrderUser('id');
        $User = App::loadModel('Core', 'User', true);
        $user = $User->findFirst(array(
            'joins' => array(
//                array(
//                    'module' => 'Core',
//                    'model' => 'UserProfile',
//                    'type' => 'left',
//                )
                array(
                    'module' => 'Mailer',
                    'model' => 'MailerGroup',
                    'type' => 'left',
                )
            ),
            'conditions' => array(
                'User.id' => $orderUserId,
            ),
            'fields' => array(
                'User.id',
                'User.email',
                'User.first_name',
                'User.last_name',
                'User.internal_name',
                'MailerGroup.name AS group_name',
            ),
        ));
        $userLabel = '';
        if ($user) {  
            $userLabel = $this->loadView('eshopOrders/userLabel', $user);
        }
        return $this->loadView('eshopOrders/setUser', array(
            'userLabel' => $userLabel,
            'searchSuggestsUrl' => App::resolveUrl(array(
                'module' => $this->module,
                'controller' => $this->name,
                'action' => 'getUserSearchSuggests',
            )),
            'setUrl' => App::resolveUrl(array(
                'module' => $this->module,
                'controller' => $this->name,
                'action' => $this->action,
            )),
        ));
    }
    
    public function getUserSearchSuggests() {
        // normalize incoming keywords into data
        $this->data['keywords'] = App::getValue($_GET['term']);
        $User = App::loadModel('Core', 'User', true);
        // apply filter to searched products
        $users = array();
        // load suggests for specified keywords
        if (($options = $User->getSearchFindOptions($this->data))) {
            // get product ids
            $options['joins'] = (array)App::getValue($options['joins']);
            $options['joins'][] = array(
                'module' => 'Mailer',
                'model' => 'MailerGroup',
                'type' => 'left',
            );
            $options['conditions'] = (array)App::getValue($options['conditions']);
            $options['conditions'] = DB::nestConditions($options['conditions']);
            $options['conditions'][] = array(
                'User.discount_price_category !=' => null, //B2B
                'OR',
                'User.id' => App::getUser('id'),
            );
            $options['key'] = 'User.id';
            $options['fields'] = array(
                'User.id',
                'User.email',
                'User.first_name',
                'User.last_name',
                'User.internal_name',
                'MailerGroup.name AS group_name',
            );
            $options['limit'] = 50;
            $users = $User->findList($options);
            foreach($users as &$user) {
                $userLabel = $this->loadView('eshopOrders/userLabel', $user);
                $user = array(
                    'id' => $user['id'],
                    'value' => $userLabel,
                    'label' => '<div class="set-order-user-suggest">' . $userLabel . '</div>',
                );
            }
            unset($user);
        }
        App::setLayout(false);
        return json_encode($users);
    }

    /**
     * Returns info for specified orders numbers.
     * Used by IS to get additional info about orders.
     * 
     * @param string $orderNumbers Single order number or comma-separated list
     *          of order numbers.
     * 
     * @return string JSON encoded associative array with order numbers set as 
     *          keys and orded info objects under the keys. If no orders
     *          are found for specified numbers then empty object "{}" is returned.
     */
    public function getInfo($orderNumbers = null, $getShipmentNumbers = 0) {
        $this->loadModel('EshopOrder');
        $Order = new EshopOrder();
        
        $orderNumbers = array_filter(Str::explode(',', $orderNumbers));
        
        $orders = array();
        if ($orderNumbers) {
            $orders = $Order->findList(array(
                'joins' => array(
                    array(
                        'type' => 'left',
                        'module' => 'Payment',
                        'model' => 'PaymentMethod',
                    ),
                    array(
                        'type' => 'left',
                        'model' => 'EshopShipmentMethod',
                    ),
                ),
                'key' => 'EshopOrder.number',
                'conditions' => array(
                    'EshopOrder.number' => $orderNumbers,
                ),
                'fields' => array(
                    'EshopOrder.payment_status',
                    'EshopOrder.shipment_numbers',
                    'PaymentMethod.pid AS payment_method_pid',
                    'EshopShipmentMethod.pid AS shipment_method_pid',
                ),
                'plain' => false,
                'order' => array(
                    'EshopOrder.number' => $orderNumbers,
                )
            ));
            $tmp = array();
            foreach($orders as $orderNumber => $order) {
                $isPaid = (int)($order['payment_status'] === 'enum_payment_paid');
                $paymentMethod = $order['payment_method_pid'];
                if (
                    $paymentMethod !== 'cash'
                    && $paymentMethod !== 'courier'
                    && $paymentMethod !== 'transfer'
                    && $paymentMethod !== 'viamo'
                ) {
                    $paymentMethod = 'online';
                }
                $shipmentMethod = $order['shipment_method_pid'];
                if ($shipmentMethod !== 'pickup') {
                    $shipmentMethod = 'delivery';
                }
                $tmp[$orderNumber] = array(
                    // 0, 1
                    'isPaid' => $isPaid,
                    // 'cash', 'courier' (cod), 'transfer', 'online' (cartdpay, gopay, ...)
                    // 'viamo' (this can be used online but also at pickup)
                    'paymentMethod' => $paymentMethod,
                    // 'pickup', 'delivery' (packeta, remax, ...)
                    'shipmentMethod' => $shipmentMethod,
                    //debug//'shipmentNumbers' => $order['shipment_numbers'],
                );
                if ($getShipmentNumbers) {
                    $tmp[$orderNumber]['shipmentNumbers'] = $order['shipment_numbers']; //debug
                }
            }
            $orders = $tmp;
        }
        
        App::setLayout(false);
        App::$allowOriginComments = false;
        header('Content-Type: text/plain');
        return json_encode((object)$orders, JSON_UNESCAPED_UNICODE);
    }

    /**
     * CRON (each 1 day)
     * 
     * Sends reminder email for each unpaid order having online payment method
     */
    public function sendPaymentReminderEmails() {
        App::setLayout(false);
        $this->loadModel('EshopOrder');
        $Order = new EshopOrder();
        $Order->sendPaymentReminderEmails();
    }
    
    /**
     * CRON (each 1 day)
     * 
     * Sends internal email with unpaid orders having online payment method older than set time.
     */
    public function sendUnpaidOrdersEmail() {
        App::setLayout(false);
        $this->loadModel('EshopOrder');
        $Order = new EshopOrder();
        $Order->sendUnpaidOrdersEmail();
    }
    
    /**
     * CRON (each 1 day)
     * 
     * Sends internal email with unconfirmed orders older than set time.
     */
    public function sendUnconfirmedOrdersEmail() {
        App::setLayout(false);
        $this->loadModel('EshopOrder');
        $Order = new EshopOrder();
        $Order->sendUnconfirmedOrdersEmail();
    }

    /**
     * CRON (each 1 day)
     * 
     * .../mvc/Eshop/EshopOrders/exportToGoogleSheets
     * 
     * Exports sold product prices statistics to Google Sheets
     */
    public function exportToGoogleSheets() {
        App::setLayout(false);
        $this->loadModel('EshopOrder');
        $Order = new EshopOrder();
        $Order->exportToGoogleSheets();
    }

    /**
     * CRON (each one day)
     * 
     * .../mvc/Eshop/EshopOrders/exportOrderCsvForSpolehliveRecenze
     */
    public function exportOrderCsvForSpolehliveRecenze() {
        App::setLayout(false);
        $this->loadModel('EshopExport');
        $EshopExport = new EshopExport();
        $EshopExport->exportOrderCsvForSpolehliveRecenze();
    }

    /**
     * Returns data for one step checkout
     * 
     * @return json checkout data
     */
    public function api_getOneStepCheckoutData() {
        App::setLayout(false);
        App::$allowOriginComments = false;
        header('Content-Type: text/plain');

        $this->loadModel('EshopOrder');
        $this->loadModel('EshopCart');
        $this->loadModel('EshopProduct');
        App::loadModel('Core', 'User');
        $Order = new EshopOrder();
        $Cart = new EshopCart();
        $Product = new EshopProduct();
        $User = new User();

        $wp = array();
        $wp['productCount'] = $Cart->countProducts();
        $wp['isQuickOrder'] = $Order->isQuickOrder();
        $wp['lang'] = App::$lang;
        $wp['countriesOptions'] = Eshop::getCountries();
        $wp['defaultCountry'] = Eshop::getDefaultCountry();
        $wp['isB2BUser'] = Eshop::hasB2BUser();
        $wp['hasBonusesAvailable'] = Eshop::hasBonusesAvailable();
        $wp['freeShipmentDeadline'] = null;
        $wp['freeShipmentOnlyForPickupPlaces'] = false;
        $wp['freeShipmentProductsTotal'] = Eshop::getOrderUserFreeShipmentProductsTotal(array(
            'deadline' => &$wp['freeShipmentDeadline'],
            'onlyForPickupPlaces' => &$wp['freeShipmentOnlyForPickupPlaces'],
        ));
        // get setting
        $wp['Settings']['companyAddress'] = $this->getSetting('EshopOrder.companyAddress');
        $wp['Settings']['openingHours'] = $this->getSetting('EshopOrder.openingHours');
        $wp['Settings']['recyclingFeeNote'] = $this->getSetting('EshopOrder.recyclingFeeNote');
        $wp['Settings']['packetaApiKey'] = $this->getSetting('EshopShipmentMethod.packetaApiKey');
        $wp['Settings']['dpdApiKey'] = $this->getSetting('EshopShipmentMethod.dpdApiKey');
        $wp['Settings']['allowNonLivePickupPoints'] = (bool)$this->getSetting('EshopShimpentMethod.allowNonLivePickupPoints');
        $wp['Settings']['Core']['UserProfile']['promotedPeriodOrderBonusPointsName'] = App::getSetting('Core', 'UserProfile.promotedPeriodOrderBonusPointsName');
        $wp['Settings']['Core']['UserProfile']['reactivationEmailOrderBonusPointsDays'] = App::getSetting('Core', 'UserProfile.reactivationEmailOrderBonusPointsDays');
        // urls
        $wp['urlCartView'] = App::getContentUrl('Eshop.EshopCarts.view');
        $wp['urlTermsAndConditions'] = App::getContentUrl('Eshop.termsAndConditions');
        $wp['urlWarrantyAndComplaintProcedure'] = App::getContentUrl('Eshop.warrantyAndComplaintProcedure');
        $wp['urlPersonalDataProcessingInfo'] = App::getContentUrl('App.personalDataProcessingInfo');
        $wp['urlSendOrder'] = App::getUrl('/mvc/Eshop/EshopOrders/api_submitCheckoutData');
        $wp['urlLogin'] = App::getContentUrl('Eshop.EshopOrders.login');
        $wp['urlBonusPointsInfo'] = App::getContentUrl('Eshop.bonusPointsInfo');
        $wp['urlCheckout'] = App::getContentUrl('Eshop.EshopOrders.checkout');
        // stock
        $wp['Stock']['STOCK'] = $Product::STOCK;

        // countdown guaranteed delivery info
        $this->loadModel('EshopCountdown');
        $Countdown = new EshopCountdown();
        $wp['CountdownDeliveryInfo'] = $Countdown->getGuaranteedDeliveryInfo();

        // user profile
        if (!$wp['isQuickOrder']) {
            $user = Eshop::getOrderUser();
            $wp['UserProfile'] = $User->findFirst(array(
                'fields' => array(
                    'User.first_name',
                    'User.last_name',
                    'User.email',
                    'UserProfile.salutation',
                    'UserProfile.degree',
                    'UserProfile.street',
                    'UserProfile.city',
                    'UserProfile.country',
                    'UserProfile.zip',
                    'UserProfile.phone',
                    'UserProfile.company_name',
                    'UserProfile.company_id_number',
                    'UserProfile.company_tax_number',
                    'UserProfile.company_vat_tax_number',
                    'UserProfile.person_type',
                    'UserProfile.another_delivery_address AS deliveryAddress',
                    'User.first_name AS delivery_first_name',
                    'User.last_name AS delivery_last_name',
                    'UserProfile.delivery_street',
                    'UserProfile.delivery_city',
                    'UserProfile.delivery_country',
                    'UserProfile.delivery_zip',
                    'UserProfile.fax',
                ),
                'conditions' => array('User.id' => $user['id']),
                'joins' => array(
                    array(
                        'model' => 'UserProfile',
                        'type' => 'left',
                    )
                ),
            ));
            if ($wp['UserProfile']['deliveryAddress']) {
                $wp['UserProfile']['deliveryAddress'] = 'otherAddress';
            }
            else {
                $wp['UserProfile']['deliveryAddress'] = 'customerAddress';
            }
            // this should not normally happen, only when DB is changed manually or
            // user person_type is changed in admin.
            if (array_key_exists('company_name', $wp) && empty($wp['company_name'])) {
                unset($wp['company_name']);
            }
            $wp['data'] = array_merge($wp, $wp['UserProfile'], (array)$Order->getOneStepCheckoutData());

            // validation of company_name before loading form to validate company_licence number and affidavit
            if (
                !$Order->validate($wp['data'], array(
                    'alternative'=> 'checkoutStep01',
                    'allowFields' => 'company_name',
                ))
            ) {
                $wp['errors']['company_name'] = $Order->getErrors('company_name'); 
            }
        }


        $cartProducts = $Cart->getProductsDetails(array('synchronize' => false));
        $cart = $Cart->getPrices($cartProducts);
        $Shipment = $this->loadModel('EshopShipmentMethod', true);
        // set conditions to retrieve shipment according to choosen delivery in previous step
        $deliveryAddress = $wp['Checkout']['checkoutStep01']['deliveryAddress'];
        $abroadDelivery = $Order->hasAbroadDelivery();
        $shipmentConditions = array('EshopShipmentMethod.active' => true);
        if ($deliveryAddress == 'merchantAddress') {
            $shipmentConditions['EshopShipmentMethod.pid'] = 'pickup';
            if (Eshop::hasB2BUser()) {
                $shipmentConditions['EshopShipmentMethod.business_type'] = array('b2b', 'any');
            }
            else {
                $shipmentConditions['EshopShipmentMethod.business_type'] = array('b2c', 'any');
            }
        }
        elseif ($abroadDelivery) {
            $shipmentConditions['EshopShipmentMethod.pid'] = 'abroadDelivery';
        }
        else {
            $shipmentConditions[] = array(
                'EshopShipmentMethod.pid' => null,
                'OR',
                array(
//                        'EshopShipmentMethod.pid !=' => 'pickup',
                    'EshopShipmentMethod.pid !=' => 'abroadDelivery'
                )
            );
            if (Eshop::hasB2BUser()) {
                $shipmentConditions['EshopShipmentMethod.business_type'] = array('b2b', 'any');
            }
            else {
                $shipmentConditions['EshopShipmentMethod.business_type'] = array('b2c', 'any');
            }
        }
        $shipmentConditions['EshopShipmentMethod.lang'] = App::$lang;
        $wp['EshopShipmentMethod'] = $Shipment->findList(array(
            'key' => 'EshopShipmentMethod.id',
            'fields' => array(
                'EshopShipmentMethod.id',
                'EshopShipmentMethod.pid',
                'EshopShipmentMethod.name',
                'EshopShipmentMethod.description',
                'EshopShipmentMethod.info',
                'EshopShipmentMethod.price',
                'EshopShipmentMethod.products_total_price_alternatives',
                'EshopShipmentMethod.package_weight_price_alternatives',
                'EshopShipmentMethod.delivery_time',
                'EshopShipmentMethod.info_locator',
                'EshopShipmentMethod.tracking_url',
                'EshopShipmentMethod.sort',
                'EshopShipmentMethod.zips',
            ),
            'conditions' => $shipmentConditions,
            'order' => 'EshopShipmentMethod.sort ASC',
        ));
        $shipmentIds = array();
        $shipmentCount = count($wp['EshopShipmentMethod']);
        $wp['unappliedVolumeLimits'] = $Cart->getUnappliedVolumeLimits();
        foreach($wp['EshopShipmentMethod'] as $i => &$shipment) {
            // get shipmentIds to retrieve only asociated payment methods
            $shipmentIds[] = $shipment['id'];
            // if there is just one shipment method or if the pickup was 
            // selected in the first step then select shipment implicitly
            if (
                $shipmentCount == 1
                || (
                    $wp['Checkout']['checkoutStep01']['deliveryAddress'] == 'merchantAddress'
                    && $shipment['pid'] == 'pickup'
                )
            ) {
                // if shipment was already set and it differs now then reset payment
                if (
                    isset($wp['data']['shipment'])
                    &&$wp['data']['shipment'] != $shipment['id']
                ) {
                    unset($wp['data']['payment']);
                }
                $wp['data']['shipment'] = $shipment['id'];
            }
            // update shipment price
            $shipment = $Shipment->getPrices($shipment, array(
                'productsPrices' => $cart,
                'forceNoPrices' => !empty($wp['unappliedVolumeLimits']),
            ));
        }
        unset($shipment); // unset reference
        if ($wp['unappliedVolumeLimits']) {
            $wp['volumeLimitMessage'] = $Cart->getVolumeLimitMessage(
                $Order->getDeliveryCountry()
            );
        }
        // get cheapest shipment prices
        $wp['cheapestShipmentPrices'] = $Shipment->getCheapestPrices();
        // payment methods
        App::loadModel('Payment', 'PaymentMethod');
        $Payment = new PaymentMethod();
        $wp['PaymentMethod'] = $Payment->findList(array(
            'key' => 'PaymentMethod.id',
            'fields' => array(                    
                'PaymentMethod.id',
                'PaymentMethod.pid',
                'PaymentMethod.name',
                'PaymentMethod.description',
                'PaymentMethod.info',
                'PaymentMethod.price',
                'PaymentMethod.products_total_price_alternatives',
                'PaymentMethod.info_locator',
            ),
            'conditions' => array(
                'PaymentMethod.active' => true,
                'EshopShipmentPaymentMethod.run_eshop_shipment_methods_id' => $shipmentIds, 
//                    'PaymentMethod.pid !=' => (isset($_GET['tp'])) ? '' : 'tatrapay', //debug
            ),
            'joins' => array(
                array(
                    'module' => 'Eshop',
                    'model' => 'EshopShipmentPaymentMethod',
                    'type' => 'left',
                )
            ),
            'order' => 'EshopShipmentPaymentMethod.id ASC',
            'group' => array('PaymentMethod.id'),
        ));
        foreach($wp['PaymentMethod'] as &$payment) {
            $payment = $Payment->getPrices($payment, array(
                'productsPrices' => $cart,
            ));
        }
        // if there is just one payment then select it implicitly
        if (count($wp['PaymentMethod']) == 1) {
            $wp['data']['payment'] = $payment['id'];
        }
        unset($payment); // unset reference
        $ShipmentPayment = $this->loadModel('EshopShipmentPaymentMethod', true);
        $wp['EshopShipmentPaymentMethod'] = $ShipmentPayment->findList(array(
            'key' => 'run_eshop_shipment_methods_id',
            'fields' => array('run_payment_methods_id'),
            'conditions' => array('run_eshop_shipment_methods_id' => $shipmentIds),
            'accumulate' => true,
        ));
        // if there is a limit of order total for COD use then remove all pairs
        // {shipmentId} => {paymentId} where this condition is not met (for actual 
        // shipment and payment prices set here above)
        $codOrderPriceLimit = $this->getSetting('EshopOrder.codOrderPriceLimit');
        if ($codOrderPriceLimit) {
            $this->loadModel('EshopCart');
            $Cart = new EshopCart();
            $cartTotals = $Cart->getPrices($Cart->getProductsDetails());
            $codPaymentId = null;
            $hasCodPayment = false;
            foreach ($wp['EshopShipmentPaymentMethod'] as $shipmentId => &$paymentIds) {
                if (!isset($wp['EshopShipmentMethod'][$shipmentId])) {
                    continue;
                }
                $shipment = $wp['EshopShipmentMethod'][$shipmentId];
                foreach ($paymentIds as $paymentIdIndex => &$paymentId) {
                    if (!isset($wp['PaymentMethod'][$paymentId])) {
                        continue;
                    }
                    $payment = $wp['PaymentMethod'][$paymentId];
                    if ($payment['pid'] === 'courier') {  //cod
                        $codPaymentId = $paymentId;
                        $orderTotal = $cartTotals['products_price_actual_taxed']
                            + $shipment['shipment_price_actual_taxed']
                            + $payment['payment_price_actual_taxed'];
                        if ($orderTotal > $codOrderPriceLimit) {
                            unset($paymentIds[$paymentIdIndex]);
                        }
                        else {
                            $hasCodPayment = true;
                        }
                    }
                }
                unset($paymentId);
                // reindex because of correct render in view
                $paymentIds = array_values($paymentIds);
                // remove shipment if there are no more payments
                if (empty($paymentIds)) {
                    unset($wp['EshopShipmentMethod'][$shipmentId]);
                }
            }
            unset($paymentIds);
            // remove COD payment if any and not used at all
            if (
                $codPaymentId !== null
                && !$hasCodPayment
            ) {
                unset($wp['PaymentMethod'][$codPaymentId]);
            }
        }

        // cart products
        $wp['EshopCartProduct'] = $Cart->getProductsDetails(array(
            'getSpecialOffers' => true,
            'synchronize' => true,
            'getDisponibilityLabel' => true,
        ));
        foreach ($wp['EshopCartProduct'] as &$product) {
            $product['urlShowDetail'] = url(array(
                'locator' => App::getContentLocator('Eshop.EshopProducts.view'),
                'args' => array($product['slug'])
            ));
        }
        unset($product);

        $wp['data']['deliveryAddress'] = App::getValue($wp['data']['deliveryAddress'], 'customerAddress');

        // get totals
        $wp['EshopCart'] = $Cart->getPrices($wp['EshopCartProduct']);
        $wp['EshopOrder'] = $Order->getPrices(array(
            'productsPrices' => $wp['EshopCart'],
            'shipmentPrices' => $wp['EshopShipmentMethod'],
            'paymentPrices' => $wp['PaymentMethod'],
            'recyclingFeePrices' => $Order->getRecyclingFeePrices(array(
                'products' => $wp['EshopCartProduct'],
            )),
        ));
        App::loadModel('Core', 'UserProfile');
        $UserProfile = new UserProfile();
        $wp['EshopCart']['received_bonus_points'] = $UserProfile->getReceivedBonusPointsFromPrice($wp['EshopCart']['products_price_to_pay']);
        $wp['EshopCart']['received_repeated_order_bonus_points'] = $UserProfile->getRepeatedOrderReceivedBonusPoints($wp['EshopCart']);
        $wp['EshopCart']['received_birthday_order_bonus_points'] = $UserProfile->getBirthdayOrderReceivedBonusPoints($wp['EshopCart']);
        $wp['EshopCart']['received_promoted_period_order_bonus_points'] = $UserProfile->getPromotedPeriodOrderReceivedBonusPoints($wp['EshopCart']);
        $wp['EshopCart']['bonus_category_names'] = null;
        $wp['EshopCart']['received_bonus_category_product_order_bonus_points'] = $UserProfile->getBonusCategoryProductOrderReceivedBonusPoints($wp['EshopCartProduct'], array(
            'bonusCategoryNames' => &$wp['EshopCart']['bonus_category_names'],
        ));
        $wp['EshopCart']['received_automatic_email_order_bonus_points' ] = $UserProfile->getAutomaticEmailOrderReceivedBonusPoints($wp['EshopCart']);

        return json_encode($wp, JSON_UNESCAPED_UNICODE);
    }

    /**
     * Validates field from checkout
     *  
     * @param string field
     * 
     * @return bool field validity
     */
    public function api_validateCheckoutField() {
        App::setLayout(false);
        App::$allowOriginComments = false;
        header('Content-Type: text/plain');

        $params = $_GET;
        $fieldName = $params['name'];
        $fieldValue = $params['value'];
        $step = $params['step'];

        $data = array(
            $fieldName => $fieldValue,
        );
        if (substr($fieldName, 0, 9) == 'delivery_') {
            $data['deliveryAddress'] = 'otherAddress';
        }

        $result = $this->Model->validateField($data, $fieldName, array(
            'alternative'=> 'checkoutStep0' . $step,
            'allowFields' => $fieldName,
        ));

        return json_encode($result, JSON_UNESCAPED_UNICODE);
    }

    public function api_validateCheckoutData() {
        App::setLayout(false);
        App::$allowOriginComments = false;
        header('Content-Type: text/plain');
        
        $params = $_GET;
        $data = json_decode(urldecode($params['data']), true);

        $dataIsValid = true;
        $validationErrors = array();

        // get pickup place
        foreach ($data as $dataFieldKey => $dataField) {
            if (substr($dataFieldKey, 0, 16) == 'pickup_place_id.') {
                $parts = explode('.', $dataFieldKey);
                $pickupPlaceShipmentId = $parts[1];
                $pickupPlaceId = $dataField;
                unset($data[$dataFieldKey]);
                $data['pickup_place_id'][$pickupPlaceShipmentId] = $pickupPlaceId;
            }
        }

        $checkoutSteps = array(
            'checkoutStep01',
            'checkoutStep02',
            'checkoutStep03',
        );
        if ($this->Model->isQuickOrder()) {
            $checkoutSteps[] = 'checkoutStep03_QuickOrder';
        }
        foreach ($checkoutSteps as $checkoutStep) {
            $dataIsValid = $this->Model->validate($data, array(
                'alternative' => $checkoutStep,
            ));
            $validationErrors = array_merge($this->Model->getErrors(), $validationErrors);
        }
        $result = $validationErrors ? $validationErrors : $dataIsValid;

        return json_encode($result, JSON_UNESCAPED_UNICODE);
    }

    public function api_submitCheckoutData() {
        App::setLayout(false);
        App::$allowOriginComments = false;
        header('Content-Type: text/plain');

        $postData = $_POST;
        $data = array(
            'checkoutStep01' => array(
                'first_name' => Sanitize::value($postData['first_name']),
                'last_name' => Sanitize::value($postData['last_name']),
                'street' => Sanitize::value($postData['street']),
                'zip' => Sanitize::value($postData['zip']),
                'city' => Sanitize::value($postData['city']),
                'country' => Sanitize::value($postData['country']),
                'email' => Sanitize::value($postData['email']),
                'phone' => Sanitize::value($postData['phone']),
                'delivery_first_name' => Sanitize::value($postData['delivery_first_name']),
                'delivery_last_name' => Sanitize::value($postData['delivery_last_name']),
                'delivery_street' => Sanitize::value($postData['delivery_street']),
                'delivery_zip' => Sanitize::value($postData['delivery_zip']),
                'delivery_city' => Sanitize::value($postData['delivery_city']),
                'delivery_country' => Sanitize::value($postData['delivery_country']),
                'delivery_email' => Sanitize::value($postData['delivery_email']),
                'delivery_phone' => Sanitize::value($postData['delivery_phone']),
                'deliveryAddress' => Sanitize::value($postData['deliveryAddress']),
            ),
            'checkoutStep02' => array(
                'shipment' => Sanitize::value($postData['shipment']),
                'payment' => Sanitize::value($postData['payment']),
            ),
            'checkoutStep03' => array(
                'business_conditions_agreement' => Sanitize::value($postData['business_conditions_agreement']),
                'personal_data_processing_agreement' => Sanitize::value($postData['personal_data_processing_agreement']),
                'UserProfile.newsletters_agreement' => Sanitize::value($postData['UserProfile_newsletters_agreement']),
                'customer_satisfaction_survey_disagreement' => Sanitize::value($postData['customer_satisfaction_survey_disagreement']),
                'adulthood_declaration' => Sanitize::value($postData['adulthood_declaration']),
                'comment' => Sanitize::value($postData['comment']),
                'dedication' => Sanitize::value($postData['dedication']),
                'includeDedication' => Sanitize::value($postData['includeDedication']),
                'recycling_fee_taxed' => Sanitize::value($postData['recycling_fee_taxed']),
                'bonus_discount_applied' => Sanitize::value($postData['bonus_discount_applied']),
            )
        );
        // add pickup place info
        $shipment = Sanitize::value($postData['shipment']);
        $pickupPlaceIdKey = 'pickup_place_id_' . $shipment;
        $pickupPlaceDataKey = 'pickup_place_data_' . $shipment;
        $data['checkoutStep02']['pickup_place_id'][$postData['shipment']] = Sanitize::value($postData[$pickupPlaceIdKey]);
        $data['checkoutStep02']['pickup_place_data'][$postData['shipment']] = Sanitize::value($postData[$pickupPlaceDataKey]);

        $this->Model->setOneStepCheckoutData($data);

        App::redirect(url(App::getContentLocator('Eshop.EshopOrders.add')));
    }

    /**
     * Return list of pickup places of specified $provider for actually checkouted order. 
     * Returned list contains pairs '{placeID}' => '{placeAddressString}'.
     * 
     * @param string $provider Pickup places provide. Only 'dpdPickup' is available.
     * 
     * @return array
     */
    public function api_getPickupPlacesList($provider) {
        App::setLayout(false);
        App::$allowOriginComments = false;
        header('Content-Type: text/plain');

        if (
            !($country = $this->data['country'])
            || !($zip = $this->data['zip'])
            || !($city = $this->data['city'])
            || !($street = $this->data['street'])
            || (
                $provider = 'dpdPickup'
                && !empty($this->getSetting('EshopShipmentMethod.dpdApiKey'))
            )
        ) {
            return null;
        }
        $pickupPlaces = array();
        if (
            $provider == 'dpdPickup'
            && ($pickupPlacesList = $this->Model->getPickupPlacesList($provider, array(
                'sort' => true,
                'pickupPlaces' => &$pickupPlaces,
                'country' => $country,
                'zip' => $zip,
                'city' => $city,
                'street' => $street,
            )))
        ) {
            $pickupPlacesData = array(
                'pickup_places_list' => $pickupPlacesList,
                'pickup_places' => $pickupPlaces,
            );
            return json_encode($pickupPlacesData, JSON_UNESCAPED_UNICODE);
        }
        return json_encode(array(), JSON_UNESCAPED_UNICODE);
    }

    public function api_getCheckoutRights() {
        App::setLayout(false);
        App::$allowOriginComments = false;
        header('Content-Type: text/plain');

        $hasCheckoutRights = $this->Model->getCheckoutRights();
        return json_encode($hasCheckoutRights, JSON_UNESCAPED_UNICODE);
    }

    /**
     * ASYNC
     * 
     * .../mvc/Eshop/EshopOrders/exportOrdersToDaktelaContacts
     * 
     * Used to call DaktelaApi::exportOrdersToDaktelaContacts() method in async way by MailerContact::import()
     */
    public function exportOrdersToDaktelaContacts() {
        App::setLayout(false);
        $this->loadLib('DaktelaApi');
        $DaktelaApi = new DaktelaApi();
        $progress = $DaktelaApi->exportOrdersToDaktelaContacts();
        return echoReadable($progress, array(
            'return' => true,
        ));
    }
}
