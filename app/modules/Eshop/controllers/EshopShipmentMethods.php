<?php

class EshopShipmentMethods extends Controller {
    
    public function __construct(){
        parent::__construct();
        $this->loadModel('EshopShipmentMethod');
    }
    
    public function admin_index() {
        $lang = !empty($_REQUEST['lang']) ? $_REQUEST['lang'] : App::$lang;
        $Res = new ExtResponse();
        
        $ShipmentMethod = new EshopShipmentMethod();
        
        $options = ExtRequest::parsePaging();  
        $options['order'][] = 'EshopShipmentMethod.business_type ASC';
        $options['order'][] = 'EshopShipmentMethod.sort ASC';
        $options = ExtRequest::parseRunFilter($ShipmentMethod, $options);
        
        // find data
        $options['fields'] = array(
            'EshopShipmentMethod.id', 
            'EshopShipmentMethod.pid', 
            'EshopShipmentMethod.heureka_code', 
            'EshopShipmentMethod.name', 
            'EshopShipmentMethod.description',
            'EshopShipmentMethod.info',
            'EshopShipmentMethod.price', 
            'EshopShipmentMethod.products_total_price_alternatives', 
            'EshopShipmentMethod.package_weight_price_alternatives', 
            'EshopShipmentMethod.sort',
            'EshopShipmentMethod.business_type',
            'EshopShipmentMethod.tracking_url',
            'EshopShipmentMethod.active',
            'EshopShipmentMethod.zips',
        );
        
        $options['conditions'] = (array)App::getValue($options['conditions']);
        $options['conditions']['EshopShipmentMethod.lang'] = $lang;
        
        $shipmentMethods = $ShipmentMethod->findAll($options);
        
        $Res->data = $shipmentMethods;
        
        // find count
        $Res->total = $ShipmentMethod->findCount(array(
            'literals' => array(
                'conditions' => $options['literals']['conditions']
            ),
            'conditions' => $options['conditions']
        ));
        
        $Res->success = true;
        return $Res->toJson();
    }
    
    public function admin_create() {
        $Res = new ExtResponse();
                
        $ShipmentMethod = new EshopShipmentMethod();
        if (($savedData = $ShipmentMethod->saveAll($this->data))) {
            $Res->data = $savedData;
            $Res->success = true;
            $Res->message = array(
                'text' => __a(__FILE__, 'Update of EshopShipmentMethod has succeeded'),
                'type' => 'info'
            );
        } 
        else {
            $Res->success = false;
            $Res->errors = $ShipmentMethod->getErrors();
            $Res->message = array(
                'text' => __a(__FILE__, 'Update of EshopShipmentMethod has failed'),
                'type' => 'error',
                'errors' => $ShipmentMethod->getErrors()
            );
        }
        return $Res->toJson();
    }  
    
    public function admin_update() {
        $Res = new ExtResponse();
                
        $ShipmentMethod = new EshopShipmentMethod();
        if (($savedData = $ShipmentMethod->saveAll($this->data))) {
            // add path to image
            $Res->data = $savedData;
            $Res->success = true;
            $Res->message = array(
                'text' => __a(__FILE__, 'Update of EshopShipmentMethod has succeeded'),
                'type' => 'info'
            );
        } 
        else {
            $Res->success = false;
            $Res->errors = $ShipmentMethod->getErrors();
            $Res->message = array(
                'text' => __a(__FILE__, 'Update of EshopShipmentMethod has failed'),
                'type' => 'error',
                'errors' => $ShipmentMethod->getErrors()
            );
        }
        return $Res->toJson();
    }    
    
    /**
     * @todo delete images too
     * @return type
     */
    public function admin_delete() {
        $Res = new ExtResponse();
        $ShipmentMethod = new EshopShipmentMethod();                     
        if ($ShipmentMethod->deleteBy('id', $this->data['id'])) {
            
            $Res->success = true;
            $Res->message = array(
                'text' => __a(__FILE__, 'Deletion of EshopShipmentMethod has succeeded'),
                'type' => 'info'
            );
            $Res->data = array();
        }
        else {
            $Res->success = false;
            $Res->errors = $ShipmentMethod->getErrors();
            $Res->message = array(
                'text' => __a(__FILE__, 'Deletion of EshopShipmentMethod has failed'),
                'type' => 'error',
                'errors' => $ShipmentMethod->getErrors()
            );
        }
        return $Res->toJson();
    }
    
    /**
     * @cron Launch once per day for each provider to update list of his available pickup places
     * 
     * @param string $provider Pickup places provide. One of 'dpdPickup'.
     */
    public function loadPickupPlaces($provider = null) {
        App::setLayout(false);
        $this->loadModel('EshopShipmentMethod');
        $ShipmentMethod = new EshopShipmentMethod();
        $provider = strtolower($provider);
        $ShipmentMethod->getPickupPlaces($provider, array(
            'reloadAll' => true,
        ));
    }    
}

