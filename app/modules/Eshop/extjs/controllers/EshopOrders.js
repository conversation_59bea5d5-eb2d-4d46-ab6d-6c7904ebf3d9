/**
 * The EshopOrders controller.
 */
Ext.define("Eshop.controllers.EshopOrders", {
    extend: 'Ext.app.Controller',
    requires: [
        'Eshop.models.EshopOrder',
        'Eshop.views.eshopOrders.Grid'
    ],
    stores: ['Eshop.stores.EshopOrders'],

    init: function () {
        this.getEshopStoresEshopOrdersStore().addListener('load', function(store, operation){
            // if total count of records in store is less than 2x records on one page,
            // store must be not buffered
            if (store.getTotalCount() <= (store.pageSize * 2)) {
                // turn buffer off
                store.buffered = false;
            } 
            else {
                // turn buffer on
                store.buffered = true;
            }
            // load filtered orders summary
            var summaryPanel = Ext.getCmp('eshoporders-summary');
            if (summaryPanel) {
                summaryPanel.update('Načítavam...');
                var summaryUrl = store.proxy.api.read;
                summaryUrl = summaryUrl.replace('/admin_index/', '/admin_getIndexSummary/');
                jQuery.ajax({
                    url: summaryUrl,
                    data: {
                        runFilter: store.getProxy().extraParams.runFilter
                    }
                }).done(function(responseJson) {
                    var response = JSON.parse(responseJson) || {};
                    var data = response.data || {};
                    var summary = data.summary || '';
                    summaryPanel.update(summary);
                });
            }
        });
        this.control({
            'eshopeshopOrdersgrid': {
                render: this.onGridRender,
                edit: this.afterEshopOrderEdit,
                beforeedit: this.onBeforeEdit,
                startedit: this.onStartEdit,
                eshopOrderEdit: this.onEshopOrderEdit,
                eshopOrderDetail: this.onEshopOrderDetail,
                eshopOrderDetailB: this.onEshopOrderDetailB,
                eshopOrderGeneratePohodaXml: this.onEshopOrderGeneratePohodaXml,
                eshopOrderDelete: this.onEshopOrderDelete,
                canceledit: this.onEshopOrderCancelEdit,
                eshopOrderRemax: this.onEshopOrderRemax,
                eshopOrder123Kurier: this.onEshopOrder123Kurier,
                eshopOrderPacketa: this.onEshopOrderPacketa,
                eshopOrderSps: this.onEshopOrderSps,
                eshopOrderDpd: this.onEshopOrderDpd,
                eshopOrderWoltDrive: this.onEshopOrderWoltDrive,
                mallOrderUpdate: this.onMallOrderUpdate
            },
            'eshopeshopOrdersgrid button#eshopeshopOrdersgrid-add-eshopOrder': {
                click: this.addEshopOrder
            },
            'eshopeshopOrdersgrid button#eshopeshopOrdersgrid-refresh': {
                click: this.reloadEshopOrdersStore
            },
            'eshopeshopOrdersgrid button#eshopeshopOrdersgrid-remove-filter': {
                click: this.removeFilter
            },
            '#EshopOrdersWindow': {
                show: this.reloadEshopOrdersStore
            }
        });
    },

    onGridRender: function () {
        //cache a reference to the eshopOrdersGrid and rowEditor
        this.eshopOrdersGrid = Ext.ComponentQuery.query('eshopeshopOrdersgrid')[0];
        this.rowEditor = this.eshopOrdersGrid.rowEditor;
    },
    
    onEshopOrderEdit: function (evtData) {
        var eshopOrderStore = this.getStore('Eshop.stores.EshopOrders');
        var record = eshopOrderStore.getAt(evtData.rowIndex);
        if(record) {
            this.rowEditor.startEdit(record, this.eshopOrdersGrid.columns[evtData.colIndex]);
        }
    },
    
    onEshopOrderDetail: function (evtData) {

        var eshopOrderStore = this.getStore('Eshop.stores.EshopOrders');
        var record = eshopOrderStore.getAt(evtData.rowIndex);
        if(record) {
            var orderMerchantPid = record.get('merchant_pid');
            var orderLang;
            if (
                orderMerchantPid === 'drinkcentrum.cz'
                || orderMerchantPid === 'mall.cz'
            ) {
                orderLang = 'cs';
            }
            else if (orderMerchantPid === 'drinkcentrum.hu') {
                orderLang = 'hu';
            }
            else if (
                orderMerchantPid === null
                || orderMerchantPid === 'rumovabanka'
                || orderMerchantPid === 'mall.sk'
                || orderMerchantPid === 'baselinker.com'
            ) {
                orderLang = 'sk';
            }
            else {
                alert(
                    'Nie je možné rozriesiť jazyk objednávky pre objednávku z :orderMerchantPid:. Kontaktujte prosím programátorov.'
                        .replace(':orderMerchantPid:', orderMerchantPid)
                );
            }
            
            if (!eshopOrderDetailWin) {
                var eshopOrderDetailWin = Ext.create('widget.window', {
                    title: __jsa('Eshop', 'Detail of order id ') + ' ' + record.get('id') ,
                    closable: true,
                    closeAction: 'destroy',
                    width: 640,
                    minWidth: 430,
                    height: 500,
                    modal: true,
                    autoScroll: true,
                    listeners: {
                        show: function() {
                            Ext.Ajax.request({
                                url: '/mvc/Eshop/EshopOrders/admin_loadWithItems?lang=' + runConfig.lang,
                                params: {
                                    orderId: record.data.id
                                },
                                success: function (response) {
                                    var jsonResponse = Ext.decode(response.responseText);
                                    eshopOrderDetailWin.down('form').getForm().setValues(jsonResponse.data);
                                    
                                    var orderMessage = Ext.getCmp('eshop-order-detail-message');
                                    if (record.data.unapplied_volume_limits) {
                                        var unappliedVolumeLimits = JSON.parse(record.data.unapplied_volume_limits);
                                        var jkpovType, unappliedVolumeLimitsDetails = '<ul>';
                                        for (jkpovType in unappliedVolumeLimits) {
                                            if (unappliedVolumeLimits.hasOwnProperty(jkpovType)) {
                                                unappliedVolumeLimitsDetails += '<li>';
                                                unappliedVolumeLimitsDetails += 
                                                    '- max ' + unappliedVolumeLimits[jkpovType] + ' ' + __jsa('Eshop', 'litrov') + ' ';
                                                if (jkpovType === 'spirits') {
                                                    unappliedVolumeLimitsDetails += __jsa('Eshop', 'liehovin');
                                                }
                                                else if (jkpovType === 'alcoholizedWine') {
                                                    unappliedVolumeLimitsDetails += __jsa('Eshop', 'alkoholizovaného vína');
                                                }
                                                else if (jkpovType === 'sparklingWine') {
                                                    unappliedVolumeLimitsDetails += __jsa('Eshop', 'šumívého vína (z celkového vína)');
                                                }
                                                else if (jkpovType === 'wine') {
                                                    unappliedVolumeLimitsDetails += __jsa('Eshop', 'vína');
                                                }
                                                else {
                                                    unappliedVolumeLimitsDetails += jkpovType;
                                                }
                                                unappliedVolumeLimitsDetails += '</li>';
                                            }
                                        }
                                        unappliedVolumeLimitsDetails += '</ul>';
                                        var orderMessageHtml = __jsa('Eshop', 'Z dôvodu uplatnenia špeciálnych ponuk sú prekročené niektoré objemové limity: :unappliedVolumeLimitsDetails:<br>Objednávku je potrebné rozdeliť do viacerých balíkov spĺňajúcich tieto limity. Nastavte prosím objednávke cenu poštovného.').replace(':unappliedVolumeLimitsDetails:', unappliedVolumeLimitsDetails);
                                        orderMessage.add([
                                            Ext.create('Ext.container.Container', {
                                                cls: 'alert-message',
                                                html: orderMessageHtml
                                            })
                                        ]);
                                    }
                                    
                                    var orderitems = Ext.getCmp('order-items-container' + record.get('id'));
                                    var orderform = eshopOrderDetailWin.down('form');
//                                    console.log(jsonResponse.data.items);
                                    var items_a = [];
                                    var items_b = [];
                                    Ext.each(jsonResponse.data.items, function(item, itemIndex){
                                        items_a.push(
                                            Ext.create('Ext.form.field.ComboBox', {
                                                width: 270,
                                                name: 'products.' + itemIndex + '.id',
////mojo: load existing order item only as static combos. Use delete + add to change existing items.
////      Avoid loading of all products list and so out of memory on PHP side                                                
//                                                store: Ext.create('Eshop.stores.EshopProductsList'),
//                                                queryMode: 'remote',
//                                                queryParam: 'runFilterByName',
//                                                minChars: 3,
                                                store: {
                                                    fields:['id', 'name'],
                                                    data: [{id: item.id, name: item.name}]
                                                }, 
                                                readOnly: true,
                                                displayField: 'name',
                                                valueField: 'id',
                                                hideTrigger: true,
                                                value: item.id,
                                                listeners: {
                                                    change: function(combo, newValue) {
                                                        if (isInteger(newValue)) {
                                                            Ext.Ajax.request({
                                                                url: '/mvc/Eshop/EshopProducts/admin_load?lang=' + runConfig.lang,
                                                                params: {
                                                                    productId: newValue,
                                                                    orderId: record.get('id')
                                                                },
                                                                success: function (productResponse) {
                                                                    var productResponse = Ext.decode(productResponse.responseText);
                                                                    combo.next().update(formatPrice(productResponse.data.price, orderMerchantPid));
                                                                    combo.next().next().update(productResponse.data.tax_rate + '%');
                                                                    combo.next().next().next().next().update(formatPrice(parseFloat(productResponse.data.price) * combo.next().next().next().getValue()), orderMerchantPid);
                                                                    Ext.getCmp('product_tax_rate' + itemIndex).setValue(productResponse.data.tax_rate);
                                                                    Ext.getCmp('product_price_taxless' + itemIndex).setValue(productResponse.data.price_taxless);
                                                                    Ext.getCmp('product_tax' + itemIndex).setValue(productResponse.data.tax);
                                                                    Ext.getCmp('product_price_actual_taxless' + itemIndex).setValue(productResponse.data.price_actual_taxless);
                                                                    Ext.getCmp('product_tax_actual' + itemIndex).setValue(productResponse.data.tax_actual);
                                                                    Ext.getCmp('product_static_attributes' + itemIndex).setValue('');
                                                                    Ext.getCmp('product_dynamic_attributes' + itemIndex).setValue('');
                                                                    eshopOrderDetailWin.calculatePrices();
                                                                }
                                                            });
                                                        }
                                                    },
                                                    afterrender:function(cmp){
                                                        if (item.internal_name) {
                                                            cmp.getEl().set({
                                                                title: item.internal_name
                                                            });
                                                            cmp.getEl().addCls('has-internal-name');
                                                        }
                                                    }
                                                }
                                            }),
                                            Ext.create('Ext.container.Container', {
                                                html: formatPrice(parseFloat(item.price_actual_taxless) + parseFloat(item.tax_actual), orderMerchantPid)
                                            }),
                                            Ext.create('Ext.container.Container', {
                                                html: item.tax_rate + '%'
                                            }),
                                            Ext.create('Ext.form.field.Text', {
                                                name: 'products.' + itemIndex + '.amount',
                                                value: item.amount,
                                                width: 30,
                                                listeners: {
                                                    change: function() {
                                                        eshopOrderDetailWin.calculatePrices();
                                                    }
                                                },
                                                cls: 'productAmount'
                                            }),
                                            Ext.create('Ext.container.Container', {
                                                html: formatPrice((parseFloat(item.price_actual_taxless) + parseFloat(item.tax_actual)) * parseFloat(item.amount), orderMerchantPid)
                                            }),
                                            Ext.create('Ext.button.Button', {
                                                icon: '/img/silk/cross.png',
                                                id: 'orderbutton' + itemIndex,
                                                handler: function(button) {
                                                    orderform.remove('product_tax_rate' + itemIndex);
                                                    orderform.remove('product_price_taxless' + itemIndex);
                                                    orderform.remove('product_tax' + itemIndex);
                                                    orderform.remove('product_price_actual_taxless' + itemIndex);
                                                    orderform.remove('product_tax_actual' + itemIndex);
                                                    orderform.remove('product_static_attributes' + itemIndex);
                                                    orderform.remove('product_dynamic_attributes' + itemIndex);
                                                    button.prev().destroy();
                                                    button.prev().destroy();
                                                    button.prev().destroy();
                                                    button.prev().destroy();
                                                    button.prev().destroy();
                                                    button.destroy();
                                                    eshopOrderDetailWin.calculatePrices();
                                                }
                                            })
                                        );
                                        items_b.push(
                                            Ext.create('Ext.form.field.Hidden', {
                                                id: 'product_tax_rate' + itemIndex,
                                                name: 'products.' + itemIndex + '.tax_rate',
                                                value: item.tax_rate,
                                            }),
                                            Ext.create('Ext.form.field.Hidden', {
                                                id: 'product_price_taxless' + itemIndex,
                                                name: 'products.' + itemIndex + '.price_taxless',
                                                value: item.price_taxless
                                            }),
                                            Ext.create('Ext.form.field.Hidden', {
                                                id: 'product_tax' + itemIndex,
                                                name: 'products.' + itemIndex + '.tax',
                                                value: item.tax
                                            }),
                                            Ext.create('Ext.form.field.Hidden', {
                                                id: 'product_price_actual_taxless' + itemIndex,
                                                name: 'products.' + itemIndex + '.price_actual_taxless',
                                                value: item.price_actual_taxless
                                            }),
                                            Ext.create('Ext.form.field.Hidden', {
                                                id: 'product_tax_actual' + itemIndex,
                                                name: 'products.' + itemIndex + '.tax_actual',
                                                value: item.tax_actual
                                            }),
                                            Ext.create('Ext.form.field.Hidden', {
                                                id: 'product_static_attributes' + itemIndex,
                                                name: 'products.' + itemIndex + '.static_attributes',
                                                value: item.static_attributes
                                            }),
                                            Ext.create('Ext.form.field.Hidden', {
                                                id: 'product_dynamic_attributes' + itemIndex,
                                                name: 'products.' + itemIndex + '.dynamic_attributes',
                                                value: item.dynamic_attributes
                                            })
                                        );
                                    });
                                    orderitems.add(items_a);
                                    orderform.add(items_b);
                                    items_a = [];
                                    items_b = [];
                                }
                            });
                        },
                        close: function() {
                            var ordersGrid = Ext.getCmp('eshopeshopOrdersgrid');
                            var selectionModel = ordersGrid.getSelectionModel();
                            var selectedRecordId = null;
                            if (selectionModel.hasSelection()) {
                                var selectedRecordId = selectionModel.getSelection()[0].getId();
                            }
                            ordersGrid.getView().saveScrollState();
                            ordersGrid.getStore().load({
                                callback: function() {
                                    ordersGrid.getView().restoreScrollState();
                                    // retore selection - find the previously selected
                                    // record by its id and if it is still present in grid
                                    // then select it back after the grid reload
                                    var selectedRecord;
                                    if (
                                        selectedRecordId
                                        && (selectedRecord = ordersGrid.getStore().getById(selectedRecordId))
                                    ) {
                                        selectionModel.select(selectedRecord);
                                    }
                                }
                            });
                        }
                    },
                    bodyPadding: 10,
                    dockedItems: {
                        xtype: 'toolbar',
                        border: false,
                        style: {
                            background: '#DFE8F6',
                            border: 0,
                            margin: 0
                        },      
                        padding: '8 8 6 8',
                        dock: 'bottom',
                        layout : {
                            type : 'hbox',
                            pack : 'center'
                        },
                        items: [{
                            xtype: 'button',
                            iconCls:'accept',
                            text: __jsa('Cms', 'Save'),
                            handler: function() {
                                var data = eshopOrderDetailWin.down('form').getForm().getFieldValues();
                                Ext.Ajax.request({
                                    url: '/mvc/Eshop/EshopOrders/admin_update/1?lang=' + runConfig.lang,
                                    params: {
                                        data: Ext.encode(data)
                                    },
                                    success: function (response) {
                                        var jsonResponse = Ext.decode(response.responseText);
                                        Ext.getCmp('eshop-eshopOrders-grid-statusbar').newStatus(jsonResponse.message);
                                    }
                                });
                            }
                        }]
                    },
                    items: [
                        {
                            xtype: 'container',
                            cls: 'eshop-order-detail-message',
                            id: 'eshop-order-detail-message'
                        }, {
                            xtype: 'form',
                            layout: {
                                type: 'column',
                                padding: 5
                            },
                            bodyStyle: {
                                background: '#DFE8F6'
                            },     
                            border: false,
                            cls: 'eshopOrderDetailForm',
                            items: [
                                {
                                xtype: 'container',
                                items: [{
                                    xtype: 'fieldset',
                                    title: __jsa('Eshop', 'Customer data'),
                                    padding: '0 10 0 10',
                                    margin: '0 0 10 0',
                                    items: [   
                                        {
                                            xtype: 'hidden',
                                            name: 'id'
                                        }, {
                                            xtype: 'textfield',
                                            name: 'fullname',
                                            fieldLabel: __jsa('Eshop', 'Full name')
                                        }, {
                                            xtype: 'textfield',
                                            name: 'company_name',
                                            fieldLabel: __jsa('Eshop', 'Company name')
                                        }, {
                                            xtype: 'textfield',
                                            name: 'company_id_number',
                                            fieldLabel: __jsa('Eshop', 'Company id number')
                                        }, {
                                            xtype: 'textfield',
                                            name: 'company_tax_number',
                                            fieldLabel: __jsa('Eshop', 'Company tax number')
                                        }, {
                                            xtype: 'textfield',
                                            name: 'company_vat_tax_number',
                                            fieldLabel: __jsa('Eshop', 'Company vat tax number')
                                        }, {
                                            xtype: 'textfield',
                                            name: 'company_alcohol_licence_number',
                                            fieldLabel: __jsa('Eshop', 'Company alcohol distribution licence number')
                                        }, {
                                            xtype: 'displayfield',
                                            name: 'private_only_alcohol_orders_affidavit',
                                            fieldLabel: __jsa('Eshop', 'Čestné prehlásenie'),
                                            renderer: function(value) {
                                                if (value !== '') {
                                                    return '<a href=' + value + ' target="_blank">' + __jsa('Eshop', 'Otvoriť') + '</a>';
                                                }
                                                return '';
                                            }
                                        }, {
                                            xtype: 'textfield',
                                            name: 'street',
                                            fieldLabel: __jsa('Eshop', 'Street')
                                        }, {
                                            xtype: 'textfield',
                                            name: 'zip',
                                            fieldLabel: __jsa('Eshop', 'Zip')
                                        }, {
                                            xtype: 'textfield',
                                            name: 'city',
                                            fieldLabel: __jsa('Eshop', 'City')
                                        }, {
                                            xtype: 'combo',
                                            name: 'country',
                                            forceSelection: true,
                                            queryMode: 'local',
                                            typeAhead: true,
                                            fieldLabel: __jsa('Eshop', 'Country'),
                                            valueField: 'pid',
                                            displayField: 'label',
                                            triggerAction: 'all',
                                            mode: 'local',                    
                                            store: new Ext.data.Store({
                                                fields: ['pid', 'label'],
                                                data: runConfig.countries                    
                                            })
                                        }, {
                                            xtype: 'textfield',
                                            name: 'email',
                                            fieldLabel: __jsa('Eshop', 'E-mail')
                                        }, {
                                            xtype: 'textfield',
                                            name: 'phone',
                                            fieldLabel: __jsa('Eshop', 'Phone')
                                        }
                                    ]
                                }, {
                                    xtype: 'fieldset',
                                    title: __jsa('Eshop', 'Shipping address'),
                                    padding: '0 10 0 10',
                                    margin: '0 0 10 0',
                                    items: [   
                                        {
                                            xtype: 'textfield',
                                            name: 'delivery_fullname',
                                            fieldLabel: __jsa('Eshop', 'Full name')
                                        }, {
                                            xtype: 'textfield',
                                            name: 'delivery_street',
                                            fieldLabel: __jsa('Eshop', 'Street')
                                        }, {
                                            xtype: 'textfield',
                                            name: 'delivery_zip',
                                            fieldLabel: __jsa('Eshop', 'Zip')
                                        }, {
                                            xtype: 'textfield',
                                            name: 'delivery_city',
                                            fieldLabel: __jsa('Eshop', 'City')
                                        }, {
                                            xtype: 'combo',
                                            name: 'delivery_country',
                                            fieldLabel: __jsa('Eshop', 'Country'),
                                            valueField: 'pid',
                                            displayField: 'label',
                                            triggerAction: 'all',
                                            mode: 'local',                    
                                            store: new Ext.data.Store({
                                                fields: ['pid', 'label'],
                                                data: runConfig.countries
                                            })
                                        }, {
                                            xtype: 'textfield',
                                            name: 'delivery_phone',
                                            fieldLabel: __jsa('Eshop', 'Phone')
                                        }
                                    ]
                                }]
                            }, {
                                xtype: 'container',
                                padding: '0 0 0 20',
                                items: [
                                {
                                    xtype: 'fieldset',
                                    title: __jsa('Eshop', 'Details'),
                                    padding: '0 10 0 10',
                                    margin: '0 0 10 0',
                                    items: [  
                                        {
                                            xtype: 'textfield',
                                            name: 'number',
                                            fieldLabel: __jsa('Eshop', 'Number of order')
                                        }, {
                                            xtype: 'combo',
                                            fieldLabel: __jsa('Eshop', 'Business type'),
                                            name: 'business_type',
                                            store: Ext.create('Ext.data.Store', {
                                                fields: ['id', 'name'],
                                                data : [
                                                    {'id': 'b2c', 'name': __jsa('Eshop', 'B2C')},
                                                    {'id': 'b2b', 'name': __jsa('Eshop', 'B2B')}
                                                ]
                                            }),
                                            valueField: 'id',
                                            displayField: 'name',
                                            forceSelection: true,
                                            value: 'b2c'
                                        }, {
                                            xtype: 'textarea',
                                            name: 'warnings',
                                            fieldLabel: __jsa('Eshop', 'Upozornenia')
                                        }, {
                                            xtype: 'textarea',
                                            name: 'comment',
                                            fieldLabel: __jsa('Eshop', 'Comment')
                                        }, {
                                            xtype: 'textarea',
                                            name: 'dedication',
                                            fieldLabel: __jsa('Eshop', 'Venovanie')
                                        }, {
                                            xtype: 'textarea',
                                            name: 'client_info',
                                            fieldLabel: __jsa('Eshop', 'Info o pripojení'),
                                            afterLabelTextTpl: '<span class="field-info" data-qtip="' + __jsa('Eshop', 'Info o internetovom pripojení, cez ktoré bola objednávka vytvorená. Toto info môže pomôcť odhaliť prípadne zablokovať pôvodcu podvodných objednávok..') + '">i</span>',
                                            readOnly: true
                                            //disabled: true,
                                        }, {
                                            xtype: 'displayfield',
                                            fieldLabel: __jsa('Eshop', 'Potvrdzovací link'),
                                            afterLabelTextTpl: '<span class="field-info" data-qtip="' + __jsa('Eshop', 'Na skopírovanie klikni') + '">i</span>',
                                            listeners: {
                                                render: function(field) {
                                                    if (record.data.status !== 'enum_unconfirmed_order') {
                                                        return;
                                                    }
                                                    var confirmOrderLink = runConfig.confirmOrderLocator[orderLang]
                                                        + '/' + record.data.id 
                                                        + '/' + record.data.token;
                                                    field.setValue('<span style="display: inline-block; max-width: 150px; word-break: break-all">' + 
                                                        confirmOrderLink + 
                                                    '</span>');
                                                },
                                                afterrender: function(field) {
                                                    if (record.data.status !== 'enum_unconfirmed_order') {
                                                        field.hide();
                                                        return;
                                                    }
                                                    var confirmOrderLink = runConfig.confirmOrderLocator[orderLang]
                                                        + '/' + record.data.id 
                                                        + '/' + record.data.token;
                                                    field.el.dom.style.cursor = 'pointer';
                                                    field.el.dom.addEventListener('click', function() {
                                                        copyTextToClipboard(confirmOrderLink, function() {
                                                            alert(__jsa(
                                                                'Eshop', 
                                                                'Potvrdzovací link bol skopírovaný'
                                                            ));
                                                        });
                                                    });
                                                }
                                            }
                                        }
                                    ]
                                }
                                ]
                            }, {
                                xtype: 'container',
                                padding: '0 0 0 20',
                                items: [
                                {
                                    xtype: 'fieldset',
                                    title: __jsa('Eshop', 'Shipping and payment') + '<span class="field-info" data-qtip="' + __jsa('Eshop', 'Možnosti, ktoré momentálne nie sú dostupné v objednávkovom procese, sú šedou farbou. Ide o možnosti, ktoré sa používali historicky.') + '">i</span>',
                                    padding: '0 10 0 10',
                                    margin: '0 0 10 0',
                                    items: [{
                                        xtype: 'container',
                                        listeners: {
                                            render: function(){
                                                var container = this, price, button, 
                                                    radiogroup, shipmentName, shipmentPrice, 
                                                    showPaymentLink = false, paymentLink;
                                                if (
                                                    typeof record.data.run_eshop_shipment_methods_id === 'undefined'
                                                ) {
                                                    return;
                                                }
                                                radiogroup = Ext.create('Ext.form.RadioGroup', {
                                                    vertical: true,
                                                    cls: 'shipping',
                                                    columns: 1,
                                                    listeners: {
                                                        change: function() {
                                                            eshopOrderDetailWin.calculatePrices();
                                                        }
                                                    }
                                                });
                                                // if external order (run_eshop_shipment_methods_id is not defined, means it equals to 0)
                                                if (!record.data.run_eshop_shipment_methods_id) {
                                                    price = parseFloat(record.data.shipment_price_taxless) + parseFloat(record.data.shipment_tax)
                                                        + parseFloat(record.data.payment_price_taxless) + parseFloat(record.data.payment_tax);
                                                
                                                    button = '';
                                                    if (
                                                        /^6|7$/.test(record.data.run_payment_methods_id) //HARDCODED :) Cardpay|Tatrapay
                                                    ) {
                                                        button += ' <button onclick="_checkPayment(' + record.data.id + ', this)">Over platbu v TB</button>';
                                                    }
                                                    radiogroup.add(
                                                        Ext.create('Ext.form.field.Radio', {
                                                            boxLabel: record.data.shipment_method_name + ' - ' + record.data.payment_method_name 
                                                                + '(' + formatPrice(price, orderMerchantPid)  + ')' + button,
                                                            name: '_shipping',
                                                            price: price,
                                                            inputValue: record.data.run_eshop_shipment_methods_id,
                                                            checked: true
                                                        })
                                                    );
                                                    container.add(radiogroup);
                                                }
                                                else if (parseInt(record.data.specific)) {
                                                    var shippingIdRegex = new RegExp('^' + record.data.run_eshop_shipment_methods_id + ';');
                                                    //console.log('specific_shipment_price', record.data.specific_shipment_price); //debug
                                                    container.add([
                                                        Ext.create('Ext.form.field.Text', {
                                                            name: 'shipment_method_name',
                                                            fieldLabel: __jsa('Eshop', 'Spôsob doručenia')
                                                        }),
                                                        Ext.create('Ext.form.field.Text', {
                                                            name: 'specific_shipment_price',
                                                            fieldLabel: __jsa('Eshop', 'Poštovné'),
                                                            cls: 'specificShipmentPrice',
                                                            // ??? it is strange that value is not loaded automatically
                                                            value: record.data.specific_shipment_price,
                                                            listeners: {
                                                                change: function() {
                                                                    eshopOrderDetailWin.calculatePrices();
                                                                }
                                                            }
                                                        })
                                                    ]);
                                                    Ext.each(runConfig.shippingOptions, function(shipping){
                                                        // preskoc na dalsiu shipping ak sa jedná o dopravu ineho jazyka
                                                        if (shipping.lang !== orderLang) {
                                                            return;
                                                        }
                                                        
                                                        var checked = false;
                                                        if (!shippingIdRegex.test(shipping.id)) {
                                                            return;
                                                        }
                                                        if (record.data.run_eshop_shipment_methods_id + ';' + record.data.run_payment_methods_id == shipping.id) {
                                                            checked = true;
                                                        }
                                                        button = '';
                                                        if (
                                                            checked && 
                                                            /;6|7$/.test(shipping.id) //HARDCODED :) Cardpay|Tatrapay
                                                        ) {
                                                            button = ' <button onclick="_checkPayment(' + record.data.id + ', this)">Over platbu v TB</button>';
                                                        }
                                                        if (
                                                            checked
                                                            && shipping.onlinePayment
                                                            && record.data.specific_shipment_price !== null
                                                            && record.data.payment_status !== 'enum_payment_paid'
                                                            && record.data.payment_status !== 'enum_payment_partially_paid'
                                                            && record.data.payment_status !== 'enum_payment_tout'
                                                        ) {
                                                            showPaymentLink = true;
                                                        }
                                                        radiogroup.add(
                                                            Ext.create('Ext.form.field.Radio', {
                                                                boxLabel: shipping.pricelessLabel + button,
                                                                name: 'shipping',
                                                                price: shipping.price,
                                                                inputValue: shipping.id,
                                                                checked: checked
                                                            })
                                                        );
                                                    });
                                                    container.add(radiogroup);
                                                }
                                                else {
                                                    Ext.each(runConfig.shippingOptions, function(shipping){
                                                        // preskoc na dalsiu shipping ak sa jedná o dopravu ineho jazyka
                                                        if (shipping.lang !== orderLang) {
                                                            return;
                                                        }
                                                        
                                                        var checked = false;
                                                        if (record.data.run_eshop_shipment_methods_id + ';' + record.data.run_payment_methods_id == shipping.id) {
                                                            checked = true;
                                                        }
                                                        
                                                        button = '';
                                                        if (
                                                            checked && 
                                                            /;6|7$/.test(shipping.id) //HARDCODED :) Cardpay|Tatrapay
                                                        ) {
                                                            button = ' <button onclick="_checkPayment(' + record.data.id + ', this)">Over platbu v TB</button>';
                                                        }
                                                        if (
                                                            checked
                                                            && shipping.onlinePayment
                                                            && record.data.payment_status !== 'enum_payment_paid'
                                                            && record.data.payment_status !== 'enum_payment_partially_paid'
                                                            && record.data.payment_status !== 'enum_payment_tout'
                                                        ) {
                                                            showPaymentLink = true;
                                                        }
                                                        radiogroup.add(
                                                            Ext.create('Ext.form.field.Radio', {
                                                                boxLabel: shipping.label + button,
                                                                name: 'shipping',
                                                                price: shipping.price,
                                                                inputValue: shipping.id,
                                                                checked: checked
                                                            })
                                                        );
                                                    });
                                                    container.add(radiogroup);
                                                }
                                                if (showPaymentLink) {
                                                    paymentLink = runConfig.paymentLocator[orderLang]
                                                        + '/' + record.data.id 
                                                        + '/' + record.data.token;
                                                    container.add([
                                                        Ext.create('Ext.form.field.Display', {
                                                            fieldLabel: __jsa(
                                                                'Eshop', 
                                                                'Platobný link (na skopírovanie klikni)'
                                                            ),
                                                            value: '<span style="word-break: break-all">' + 
                                                                        paymentLink + 
                                                                    '</span>',
                                                            listeners: {
                                                                afterrender: function() {
                                                                    this.el.dom.style.cursor = 'pointer';
                                                                    this.el.dom.addEventListener('click', function() {
                                                                        copyTextToClipboard(paymentLink, function() {
                                                                            alert(__jsa(
                                                                                'Eshop', 
                                                                                'Platobný link bol skopírovaný'
                                                                            ));
                                                                        });
                                                                    });
                                                                }
                                                            }
                                                        })
                                                    ]);
                                                }
                                            },
                                        }
                                    }]
                                }]
                            }, {
                                xtype: 'container',
                                flex: 1,
                                padding: '0 0 0 0',
                                items: [
                                {
                                    xtype: 'fieldset',
                                    flex: 1,
                                    title: __jsa('Eshop', 'Items'),
                                    padding: '0 10 0 10',
                                    margin: '0 0 10 0',
                                    items: [{
                                        xtype: 'container',
                                        id: 'order-items-container' + record.get('id'),
                                        padding: '0 0 0 0',
                                        layout: {
                                            type: 'table',
                                            columns: 6,
                                            tdAttrs: {
                                                style: {
                                                    padding: '0 10px 0 0'
                                                }
                                            },
                                            tableAttrs: {
                                                style: {
                                                    width: '550px'
                                                }
                                            }
                                        },
                                        defaultType: 'container',
                                        defaults: {
                                            layout: {
                                                type: 'fit'
                                            }
                                        },
                                        items: [
                                            {
                                                html: __jsa('Eshop', 'Name'),
                                                width: 270
                                            },{
                                                html: __jsa('Eshop', 'Price with VAT'),
                                                width: 60
                                            },{
                                                html: __jsa('Eshop', 'Tax rate')
                                            },{
                                                html: __jsa('Eshop', 'Amount')
                                            },{
                                                html: __jsa('Eshop', 'Price total with VAT'),
                                                width: 60
                                            },{
                                                html: '',
                                                width: 20
                                            },
                                        ]    
                                    }, {
                                        xtype: 'button',
                                        icon: '/img/silk/add.png',
                                        text: __jsa('Eshop', 'Add product item'),
                                        style: {
                                            margin: '0 0 10px 0'
                                        },
                                        handler: function() {
                                            var orderitems = Ext.getCmp('order-items-container' + record.get('id'));
                                            var orderform = eshopOrderDetailWin.down('form');
                                            var itemIndex = Math.floor((Math.random()*1000)+1000);
                                            orderitems.add(
                                                Ext.create('Ext.form.field.ComboBox', {
                                                    width: 270,
                                                    name: 'products.' + itemIndex + '.id',
                                                    store: Ext.create('Eshop.stores.EshopProductsList'),
                                                    displayField: 'name',
                                                    valueField: 'id',
                                                    queryMode: 'remote',
                                                    queryParam: 'runFilterByName',
                                                    hideTrigger: true,
                                                    minChars: 3,
                                                    listeners: {
                                                        change: function(combo, newValue) {
                                                            if (isInteger(newValue)) {
                                                                Ext.Ajax.request({
                                                                    url: '/mvc/Eshop/EshopProducts/admin_load?lang=' + runConfig.lang,
                                                                    params: {
                                                                        productId: newValue,
                                                                        orderId: record.get('id')
                                                                    },
                                                                    success: function (productResponse) {
                                                                        var productResponse = Ext.decode(productResponse.responseText);
                                                                        combo.next().update(formatPrice(productResponse.data.price, orderMerchantPid));
                                                                        combo.next().next().update(productResponse.data.tax_rate + '%');
                                                                        combo.next().next().next().next().update(formatPrice(parseFloat(productResponse.data.price) * combo.next().next().next().getValue(), orderMerchantPid));
                                                                        Ext.getCmp('product_tax_rate' + itemIndex).setValue(productResponse.data.tax_rate);
                                                                        Ext.getCmp('product_price_taxless' + itemIndex).setValue(productResponse.data.price_taxless);
                                                                        Ext.getCmp('product_tax' + itemIndex).setValue(productResponse.data.tax);
                                                                        Ext.getCmp('product_price_actual_taxless' + itemIndex).setValue(productResponse.data.price_actual_taxless);
                                                                        Ext.getCmp('product_tax_actual' + itemIndex).setValue(productResponse.data.tax_actual);
                                                                        Ext.getCmp('product_static_attributes' + itemIndex).setValue('');
                                                                        Ext.getCmp('product_dynamic_attributes' + itemIndex).setValue('');
                                                                        eshopOrderDetailWin.calculatePrices();
                                                                    }
                                                                });
                                                            }
                                                        }
                                                    }
                                                }),
                                                Ext.create('Ext.container.Container', {
                                                    html: formatPrice(0, orderMerchantPid)
                                                }),
                                                Ext.create('Ext.container.Container', {
                                                    html: ''
                                                }),
                                                Ext.create('Ext.form.field.Text', {
                                                    name: 'products.' + itemIndex + '.amount',
                                                    value: 1,
                                                    width: 30,
                                                    cls: 'productAmount',
                                                    listeners: {
                                                        change: function() {
                                                            eshopOrderDetailWin.calculatePrices();
                                                        }
                                                    },
                                                }),
                                                Ext.create('Ext.container.Container', {
                                                    html: formatPrice(0, orderMerchantPid)
                                                }),
                                                Ext.create('Ext.button.Button', {
                                                    icon: '/img/silk/cross.png',
                                                    id: 'orderbutton' + itemIndex,
                                                    handler: function(button) {
                                                        orderform.remove('product_tax_rate' + itemIndex);
                                                        orderform.remove('product_price_taxless' + itemIndex);
                                                        orderform.remove('product_tax' + itemIndex);
                                                        orderform.remove('product_price_actual_taxless' + itemIndex);
                                                        orderform.remove('product_tax_actual' + itemIndex);
                                                        orderform.remove('product_static_attributes' + itemIndex);
                                                        orderform.remove('product_dynamic_attributes' + itemIndex);
                                                        button.prev().destroy();
                                                        button.prev().destroy();
                                                        button.prev().destroy();
                                                        button.prev().destroy();
                                                        button.prev().destroy();
                                                        button.destroy();
                                                        eshopOrderDetailWin.calculatePrices();
                                                    }
                                                })
                                            );
                                            orderform.add(
                                                Ext.create('Ext.form.field.Hidden', {
                                                    id: 'product_tax_rate' + itemIndex,
                                                    name: 'products.' + itemIndex + '.tax_rate'
                                                }),
                                                Ext.create('Ext.form.field.Hidden', {
                                                    id: 'product_price_taxless' + itemIndex,
                                                    name: 'products.' + itemIndex + '.price_taxless'
                                                }),
                                                Ext.create('Ext.form.field.Hidden', {
                                                    id: 'product_tax' + itemIndex,
                                                    name: 'products.' + itemIndex + '.tax'
                                                }),
                                                Ext.create('Ext.form.field.Hidden', {
                                                    id: 'product_price_actual_taxless' + itemIndex,
                                                    name: 'products.' + itemIndex + '.price_actual_taxless'
                                                }),
                                                Ext.create('Ext.form.field.Hidden', {
                                                    id: 'product_tax_actual' + itemIndex,
                                                    name: 'products.' + itemIndex + '.tax_actual'
                                                }),
                                                Ext.create('Ext.form.field.Hidden', {
                                                    id: 'product_static_attributes' + itemIndex,
                                                    name: 'products.' + itemIndex + '.static_attributes',
                                                    value: ''
                                                }),
                                                Ext.create('Ext.form.field.Hidden', {
                                                    id: 'product_dynamic_attributes' + itemIndex,
                                                    name: 'products.' + itemIndex + '.dynamic_attributes',
                                                    value: ''
                                                })
                                            );
                                        }
                                    }]
                                }, {
                                    xtype: 'fieldset',
                                    flex: 1,
                                    title: __jsa('Eshop', 'Environmentálny poplatok'),
                                    padding: '0 10 0 10',
                                    margin: '0 0 10 0',
                                    listeners: {
                                        render: function() {
                                            if (
                                                record.data.recycling_fee_taxless == 0 
                                                || record.data.recycling_fee_taxless == null
                                            ) {
                                                this.hide();
                                            }
                                        }
                                    },
                                    items: [{
                                        xtype: 'container',
                                        cls: 'recyclingFee',
                                        style: {
                                            fontSize: '16px',
                                            fontWeight: 'bold'
                                        },
                                        listeners: {
                                            beforerender: function() {
                                                if (record.data.business_type == 'b2b') {
                                                    this.update(formatPrice(record.data.recycling_fee_taxless, orderMerchantPid));
                                                }
                                                else {
                                                    var price = parseFloat(record.data.recycling_fee_taxless) + parseFloat(record.data.recycling_fee_tax);
                                                    this.update(formatPrice(price, orderMerchantPid));
                                                }
                                            }
                                        },
                                    }]
                                }, {
                                    xtype: 'fieldset',
                                    flex: 1,
                                    title: __jsa('Eshop', 'Bonus'),
                                    padding: '0 10 0 10',
                                    margin: '0 0 10 0',
                                    listeners: {
                                        render: function() {
                                            if (
                                                record.data.bonus_discount == 0 
                                                || record.data.bonus_discount == null
                                            ) {
                                                this.hide();
                                            }
                                        }
                                    },
                                    items: [{
                                        xtype: 'container',
                                        style: {
                                            fontSize: '16px',
                                            fontWeight: 'bold'
                                        },
                                        html: formatPrice(-record.data.bonus_discount, orderMerchantPid)
                                    }]
                                }, {
                                    xtype: 'fieldset',
                                    flex: 1,
                                    title: __jsa('Eshop', 'Total for order'),
                                    padding: '0 10 0 10',
                                    margin: '0 0 10 0',
                                    items: [{
                                        xtype: 'container',
                                        cls: 'totalPrice',
                                        style: {
                                            fontSize: '16px',
                                            fontWeight: 'bold'
                                        },
                                        html: formatPrice(record.data.order_price_to_pay, orderMerchantPid)
                                    }]
                                }]
                            }]
                        }
                    ],
                    calculatePrices: function() {
                        var total = 0, value;
                        // check all relevant fields of form and calculate prices
                        var fields = eshopOrderDetailWin.down('form').getForm().getFields();
                        Ext.each(fields.items, function(fieldObject) {
                            if (fieldObject.cls == 'shipping') {
                                Ext.each(fieldObject.items.items, function(radioObject){
                                    if (radioObject.checked === true) total += parseFormattedPrice(radioObject.price);
                                });
                            }
                            if (fieldObject.cls == 'specificShipmentPrice') {
                                value = parseFormattedPrice(fieldObject.getValue());
                                if (value) {
                                    total += value;
                                }
                            }
                            if (fieldObject.cls == 'productAmount') {
                                var totalProductPrice = parseFloat(fieldObject.getValue()) * parseFormattedPrice(fieldObject.prev().prev().el.dom.innerHTML);
                                fieldObject.next().update(formatPrice(totalProductPrice, orderMerchantPid));
                                total += totalProductPrice;
                            }
                        });
                        eshopOrderDetailWin.down('container[cls="totalPrice"]').update(formatPrice(total, orderMerchantPid));
                    }
                });
                eshopOrderDetailWin.show();
//                detailWin.showAt(20, 90);
                
            } else {
                eshopOrderDetailWin.hide(null, function() {
                    
                });
            }
        }
    },

    onEshopOrderRemax: function (evtData) {

        var eshopOrderStore = this.getStore('Eshop.stores.EshopOrders');
        var record = eshopOrderStore.getAt(evtData.rowIndex);

        var hide = (
            record.get('merchant_pid') === 'drinkcentrum.cz'
//            || record.get('merchant_pid') === 'drinkcentrum.hu' //@todo-hu
        );
        
        if(record) {
            if (!eshopOrderDetailWin) {
                var eshopOrderDetailWin = Ext.create('widget.window', {
                    title: 'Objednávka prepravy ReMax pre obj. ' + record.get('number') ,
                    closable: true,
                    closeAction: 'destroy',
                    width: 780,
                    minWidth: 430,
                    height: 540,
                    modal: true,
                    autoScroll: true,
                    listeners: {
                        show: function() {
                            Ext.Ajax.request({
                                url: '/mvc/Eshop/EshopOrders/admin_loadWithItemsForRemax?lang=' + runConfig.lang,
                                params: {
                                    orderId: record.data.id
                                },
                                success: function (response) {
                                    var jsonResponse = Ext.decode(response.responseText);
                                    eshopOrderDetailWin.down('form').getForm().setValues(jsonResponse.data);
                                    if (jsonResponse.data.dobierka) {
                                        var dobierkaField = eshopOrderDetailWin.down('form').getForm().findField('dobierka');
                                        if (dobierkaField) {
                                            var dobierkaLabel = dobierkaField.getFieldLabel();
                                            dobierkaLabel += ' (' + jsonResponse.data.dobierka + ')';
                                            dobierkaField.setFieldLabel(dobierkaLabel);
                                        }
                                    }
                                    var message = jsonResponse.message;
                                    if (message) {
                                        if (jsonResponse.success) {
                                            message = '<img style="vertical-align: top; width: 16px;" src="/img/silk/accept.png" alt="OK" /> ' + message;
                                        } else {
                                            message = '<img style="vertical-align: top; width: 16px;" src="/img/silk/exclamation.png" alt="OK" /> ' + message;
                                        }
                                        eshopOrderDetailWin.down('#RemaxApiAnswer').update(message);
                                    }
                                }
                            });
                        },
                        close: function() {
                            var ordersGrid = Ext.getCmp('eshopeshopOrdersgrid');
                            var selectionModel = ordersGrid.getSelectionModel();
                            var selectedRecordId = null;
                            if (selectionModel.hasSelection()) {
                                var selectedRecordId = selectionModel.getSelection()[0].getId();
                            }
                            ordersGrid.getView().saveScrollState();
                            ordersGrid.getStore().load({
                                callback: function() {
                                    ordersGrid.getView().restoreScrollState();
                                    // retore selection - find the previously selected
                                    // record by its id and if it is still present in grid
                                    // then select it back after the grid reload
                                    var selectedRecord;
                                    if (
                                        selectedRecordId
                                        && (selectedRecord = ordersGrid.getStore().getById(selectedRecordId))
                                    ) {
                                        selectionModel.select(selectedRecord);
                                    }
                                }
                            });
                        }
                    },
                    bodyPadding: 10,
                    dockedItems: {
                        xtype: 'toolbar',
                        border: false,
                        style: {
                            background: '#DFE8F6',
                            border: 0,
                            margin: 0
                        },
                        padding: '8 8 6 8',
                        dock: 'bottom',
                        layout : {
                            type : 'hbox',
                            pack : 'center'
                        },
                        items: [{
                            xtype: 'button',
                            iconCls: 'accept',
                            id: 'orderRemaxButton',
                            text: 'Objednať prepravu ReMax',
                            handler: function() {
                                var data = eshopOrderDetailWin.down('form').getForm().getFieldValues();
                                Ext.Ajax.request({
                                    url: '/mvc/Eshop/EshopOrders/admin_orderRemax?lang=' + runConfig.lang,
                                    params: {
                                        data: Ext.encode(data)
                                    },
                                    success: function (response) {
                                        eshopOrderDetailWin.enable();
                                        var jsonResponse = Ext.decode(response.responseText);
                                        var message = jsonResponse.message;
                                        if (jsonResponse.success) {
                                            message = '<img style="vertical-align: top; width: 16px;" src="/img/silk/accept.png" alt="OK" /> ' + message;
                                            eshopOrderDetailWin.down('#orderRemaxButton').disable();
                                        } else {
                                            message = '<img style="vertical-align: top; width: 16px;" src="/img/silk/exclamation.png" alt="OK" /> ' + message;
                                        }
                                        eshopOrderDetailWin.down('#RemaxApiAnswer').update(message);
                                    }
                                });
                                eshopOrderDetailWin.disable();
                            }
                        }]
                    },
                    items: [
                        {
                            xtype: 'container',
                            id: 'RemaxApiAnswer',
                            html: '',
                            style: {
                                background: '#f1f6a5',
                                padding: '15px',
                                border: '1px solid #b5b8c8',
                                margin: '0 0 10px 0'
                            },
                        },
                        {
                            xtype: 'form',
                            layout: {
                                type: 'column',
                                padding: 5
                            },
                            bodyStyle: {
                                background: '#DFE8F6'
                            },
                            border: false,
                            cls: 'eshopOrderDetailForm',
                            items: [
                                {
                                    xtype: 'container',
                                    items: [{
                                        xtype: 'fieldset',
                                        title: 'Príjemca',
                                        padding: '0 10 0 10',
                                        margin: '0 0 10 0',
                                        defaults: {
                                            width: 300
                                        },
                                        items: [
                                            {
                                                xtype: 'hidden',
                                                name: 'id'
                                            }, {
                                                xtype: 'textfield',
                                                name: 'fullname',
                                                fieldLabel: 'Názov príjemcu'
                                            }, {
                                                xtype: 'textfield',
                                                name: 'street',
                                                fieldLabel: 'Ulica, číslo'
                                            }, {
                                                xtype: 'textfield',
                                                name: 'zip',
                                                fieldLabel: 'PSČ'
                                            }, {
                                                xtype: 'textfield',
                                                name: 'city',
                                                fieldLabel: 'Mesto'
                                            }, {
                                                xtype: 'combo',
                                                forceSelection: true,
                                                queryMode: 'local',
                                                typeAhead: true,
                                                name: 'country',
                                                fieldLabel: 'Štát',
                                                valueField: 'pid',
                                                displayField: 'label',
                                                triggerAction: 'all',
                                                mode: 'local',
                                                store: new Ext.data.Store({
                                                    fields: ['pid', 'label'],
                                                    data: runConfig.countries
                                                })
                                            }, {
                                                xtype: 'textfield',
                                                name: 'phone',
                                                fieldLabel: __jsa('Eshop', 'Phone')
                                            }, {
                                                xtype: 'textfield',
                                                name: 'contact_person',
                                                fieldLabel: 'Kont. osoba'
                                            }, {
                                                xtype: 'textfield',
                                                name: 'email',
                                                fieldLabel: __jsa('Eshop', 'E-mail')
                                            }, {
                                                    xtype: 'textarea',
                                                    name: 'comment',
                                                    fieldLabel: __jsa('Eshop', 'Comment')
                                            }
                                        ]
                                    }]
                                }, {
                                    xtype: 'container',
                                    padding: '0 0 0 20',
                                    items: [
                                        {
                                            xtype: 'fieldset',
                                            title: 'Zásielka',
                                            padding: '0 10 0 10',
                                            margin: '0 0 10 0',
                                            defaults: {
                                                labelWidth: 160,
                                                width: 350
                                            },
                                            items: [
                                                {
                                                    xtype: 'textfield',
                                                    name: 'value',
                                                    fieldLabel: 'Hodnota',
                                                }, {
                                                    xtype: 'textfield',
                                                    name: 'weight',
                                                    fieldLabel: 'Hmotnosť (kg)',
                                                }, {
                                                    xtype: 'textfield',
                                                    name: 'package_count',
                                                    fieldLabel: 'Počet kusov (balíkov)',
                                                    value: '1'
                                                }, {
                                                    xtype: 'textfield',
                                                    name: 'vs',
                                                    fieldLabel: 'Variabilný symbol',
                                                }, {
                                                    xtype: 'textfield',
                                                    name: 'dobierka',
                                                    fieldLabel: 'Dobierka',
                                                }, {
                                                    xtype: 'combo',
                                                    fieldLabel: 'Mena pre dobierku',
                                                    name: 'dobierka_mena',
                                                    store: Ext.create('Ext.data.Store', {
                                                        fields: ['id', 'name'],
                                                        data : [
                                                            {'id': 'EUR', 'name': 'EUR'},
                                                            {'id': 'CZK', 'name': 'CZK'}
                                                            //@todo-hu
                                                        ]
                                                    }),
                                                    valueField: 'id',
                                                    displayField: 'name',
                                                    forceSelection: true,
                                                    value: 'EUR'
                                                }, {
                                                    xtype: 'textfield',
                                                    name: 'dobierka_cislo_uctu',
                                                    fieldLabel: 'Číslo účtu pre dobierku',
                                                }, {
                                                    xtype: 'textfield',
                                                    name: 'dobierka_vs',
                                                    fieldLabel: 'Variabilný symbol dobierka',
                                                }, {
                                                    xtype: 'textarea',
                                                    name: 'description',
                                                    fieldLabel: 'Popis zásielky'
                                                }, {
                                                    xtype: 'combo',
                                                    fieldLabel: 'SMS notifikácia',
                                                    name: 'doplnkove_sluzby',
                                                    store: Ext.create('Ext.data.Store', {
                                                        fields: ['id', 'name'],
                                                        data : [
                                                            {'id': '1', 'name': 'Áno'},
                                                            {'id': '', 'name': 'Nie'}
                                                        ]
                                                    }),
                                                    valueField: 'id',
                                                    displayField: 'name',
                                                    forceSelection: true,
                                                    value: '1'
                                                }, {
                                                    xtype: 'combo',
                                                    fieldLabel: 'Dodací list',
                                                    name: 'dodaci_list',
                                                    store: Ext.create('Ext.data.Store', {
                                                        fields: ['id', 'name'],
                                                        data : [
                                                            {'id': 'ano', 'name': 'Áno'},
                                                            {'id': 'nie', 'name': 'Nie'}
                                                        ]
                                                    }),
                                                    valueField: 'id',
                                                    displayField: 'name',
                                                    forceSelection: true,
                                                    value: 'nie',
                                                    hidden: hide
                                                }
                                            ]
                                        }
                                    ]
                                }
                            ]
                        }
                    ],
                });
                eshopOrderDetailWin.show();
//                detailWin.showAt(20, 90);

            } else {
                eshopOrderDetailWin.hide(null, function() {

                });
            }
        }
    },
    
    onEshopOrder123Kurier: function (evtData) {

        var eshopOrderStore = this.getStore('Eshop.stores.EshopOrders');
        var record = eshopOrderStore.getAt(evtData.rowIndex);

        
        if(record) {
            if (!eshopOrderDetailWin) {
                var eshopOrderDetailWin = Ext.create('widget.window', {
                    title: 'Objednávka prepravy 123Kurier pre obj. ' + record.get('number') ,
                    closable: true,
                    closeAction: 'destroy',
                    width: 780,
                    minWidth: 430,
                    height: 480,
                    modal: true,
                    autoScroll: true,
                    listeners: {
                        show: function() {
                            Ext.Ajax.request({
                                url: '/mvc/Eshop/EshopOrders/admin_loadWithItemsFor123Kurier?lang=' + runConfig.lang,
                                params: {
                                    orderId: record.data.id
                                },
                                success: function (response) {
                                    var jsonResponse = Ext.decode(response.responseText);
                                    eshopOrderDetailWin.down('form').getForm().setValues(jsonResponse.data);
                                    var message = jsonResponse.message;
                                    if (message) {
                                        if (jsonResponse.success) {
                                            message = '<img style="vertical-align: top; width: 16px;" src="/img/silk/accept.png" alt="OK" /> ' + message;
                                        } else {
                                            message = '<img style="vertical-align: top; width: 16px;" src="/img/silk/exclamation.png" alt="OK" /> ' + message;
                                        }
                                        eshopOrderDetailWin.down('#123KurierApiAnswer').update(message);
                                    }
                                }
                            });
                        },
                        close: function() {
                            var ordersGrid = Ext.getCmp('eshopeshopOrdersgrid');
                            var selectionModel = ordersGrid.getSelectionModel();
                            var selectedRecordId = null;
                            if (selectionModel.hasSelection()) {
                                var selectedRecordId = selectionModel.getSelection()[0].getId();
                            }
                            ordersGrid.getView().saveScrollState();
                            ordersGrid.getStore().load({
                                callback: function() {
                                    ordersGrid.getView().restoreScrollState();
                                    // retore selection - find the previously selected
                                    // record by its id and if it is still present in grid
                                    // then select it back after the grid reload
                                    var selectedRecord;
                                    if (
                                        selectedRecordId
                                        && (selectedRecord = ordersGrid.getStore().getById(selectedRecordId))
                                    ) {
                                        selectionModel.select(selectedRecord);
                                    }
                                }
                            });
                        }
                    },
                    bodyPadding: 10,
                    dockedItems: {
                        xtype: 'toolbar',
                        border: false,
                        style: {
                            background: '#DFE8F6',
                            border: 0,
                            margin: 0
                        },
                        padding: '8 8 6 8',
                        dock: 'bottom',
                        layout : {
                            type : 'hbox',
                            pack : 'center'
                        },
                        items: [{
                            xtype: 'button',
                            iconCls: 'accept',
                            id: 'order123KurierButton',
                            text: 'Objednať prepravu 123Kuriér',
                            handler: function() {
                                var data = eshopOrderDetailWin.down('form').getForm().getFieldValues();
                                Ext.Ajax.request({
                                    url: '/mvc/Eshop/EshopOrders/admin_order123Kurier?lang=' + runConfig.lang,
                                    params: {
                                        data: Ext.encode(data)
                                    },
                                    success: function (response) {
                                        eshopOrderDetailWin.enable();
                                        var jsonResponse = Ext.decode(response.responseText);
                                        var message = jsonResponse.message;
                                        if (jsonResponse.success) {
                                            message = '<img style="vertical-align: top; width: 16px;" src="/img/silk/accept.png" alt="OK" /> ' + message;
                                            eshopOrderDetailWin.down('#order123KurierButton').disable();
                                        } else {
                                            message = '<img style="vertical-align: top; width: 16px;" src="/img/silk/exclamation.png" alt="OK" /> ' + message;
                                        }
                                        eshopOrderDetailWin.down('#123KurierApiAnswer').update(message);
                                    }
                                });
                                eshopOrderDetailWin.disable();
                            }
                        }]
                    },
                    items: [
                        {
                            xtype: 'container',
                            id: '123KurierApiAnswer',
                            html: '',
                            style: {
                                background: '#f1f6a5',
                                padding: '15px',
                                border: '1px solid #b5b8c8',
                                margin: '0 0 10px 0'
                            },
                        },
                        {
                            xtype: 'form',
                            layout: {
                                type: 'column',
                                padding: 5
                            },
                            bodyStyle: {
                                background: '#DFE8F6'
                            },
                            border: false,
                            cls: 'eshopOrderDetailForm',
                            items: [
                                {
                                    xtype: 'container',
                                    items: [{
                                        xtype: 'fieldset',
                                        title: 'Príjemca',
                                        padding: '0 10 0 10',
                                        margin: '0 0 10 0',
                                        defaults: {
                                            width: 300
                                        },
                                        items: [
                                            {
                                                xtype: 'hidden',
                                                name: 'id'
                                            }, {
                                                xtype: 'textfield',
                                                name: 'fullname',
                                                fieldLabel: 'Názov príjemcu'
                                            }, {
                                                xtype: 'textfield',
                                                name: 'street',
                                                fieldLabel: 'Ulica, číslo'
                                            }, {
                                                xtype: 'textfield',
                                                name: 'zip',
                                                fieldLabel: 'PSČ'
                                            }, {
                                                xtype: 'textfield',
                                                name: 'city',
                                                fieldLabel: 'Mesto'
                                            }, {
                                                xtype: 'combo',
                                                forceSelection: true,
                                                queryMode: 'local',
                                                typeAhead: true,
                                                name: 'country',
                                                fieldLabel: 'Štát',
                                                valueField: 'pid',
                                                displayField: 'label',
                                                triggerAction: 'all',
                                                mode: 'local',
                                                store: new Ext.data.Store({
                                                    fields: ['pid', 'label'],
                                                    data: runConfig.countries
                                                })
                                            }, {
                                                xtype: 'textfield',
                                                name: 'phone',
                                                fieldLabel: __jsa('Eshop', 'Phone')
                                            }, {
                                                xtype: 'textfield',
                                                name: 'contact_person',
                                                fieldLabel: 'Kont. osoba'
                                            }, {
                                                xtype: 'textfield',
                                                name: 'email',
                                                fieldLabel: __jsa('Eshop', 'E-mail')
//                                            }, {
//                                                xtype: 'textarea',
//                                                name: 'comment',
//                                                fieldLabel: __jsa('Eshop', 'Comment')
                                            }
                                        ]
                                    }]
                                }, {
                                    xtype: 'container',
                                    padding: '0 0 0 20',
                                    items: [
                                        {
                                            xtype: 'fieldset',
                                            title: 'Zásielka',
                                            padding: '0 10 0 10',
                                            margin: '0 0 10 0',
                                            defaults: {
                                                labelWidth: 160,
                                                width: 350
                                            },
                                            items: [
                                                {
                                                    xtype: 'textfield',
                                                    name: 'value',
                                                    fieldLabel: 'Hodnota poistenia',
                                                    afterLabelTextTpl: '<span class="field-info" data-qtip="' + __jsa('Eshop', 'Poistenie balíka do hodnoty 2000€ je v cene služby. Uvádzajte tu len hodnoty väčšie ako 2000€. Cena za pripoistenie nad 2000€ je 1% z hodnoty poistenia. Pre objednávky z drinkcentrum.cz toto pole netreba vôbec vyplňať.') + '">i</span>'
                                                }, {
                                                    xtype: 'textfield',
                                                    name: 'weight',
                                                    fieldLabel: 'Hmotnosť [kg]',
                                                    afterLabelTextTpl: '<span class="field-info" data-qtip="' + __jsa('Eshop', 'Nie je nutné vypĺňať, vážia si to sami. Max povolená hmotnosť balíka je 40kg.') + '">i</span>'
                                                }, {
                                                    xtype: 'textfield',
                                                    name: 'package_count',
                                                    fieldLabel: 'Počet kusov (balíkov)',
                                                    value: '1'
                                                }, {
                                                    xtype: 'textfield',
                                                    name: 'reference',
                                                    fieldLabel: 'Referencia',
                                                    afterLabelTextTpl: '<span class="field-info" data-qtip="' + __jsa('Eshop', 'Referencia na objednávku alebo faktúru. Bude uvedená pri jednotlivých položkách faktúry za kuriérske služby.') + '">i</span>'
                                                }, {
                                                    xtype: 'textfield',
                                                    name: 'dobierka',
                                                    fieldLabel: 'Dobierka'
                                                }, {
                                                    xtype: 'textfield',
                                                    name: 'dobierka_cislo_uctu',
                                                    fieldLabel: 'Číslo účtu pre dobierku'
                                                }, {
                                                    xtype: 'textfield',
                                                    name: 'dobierka_vs',
                                                    fieldLabel: 'Variabilný symbol dobierka'
                                                }, {
                                                    xtype: 'textarea',
                                                    name: 'description',
                                                    fieldLabel: 'Popis zásielky',
                                                    afterLabelTextTpl: '<span class="field-info" data-qtip="' + __jsa('Eshop', 'Poznámka na etikete balíka viditeľná pre vodiča aj pre príjemcu') + '">i</span>'
                                                }, {
                                                    xtype: 'combo',
                                                    fieldLabel: 'SMS notifikácia',
                                                    name: 'sms_notification',
                                                    store: Ext.create('Ext.data.Store', {
                                                        fields: ['id', 'name'],
                                                        data : [
                                                            {'id': '1', 'name': 'Áno'},
                                                            {'id': '', 'name': 'Nie'}
                                                        ]
                                                    }),
                                                    valueField: 'id',
                                                    displayField: 'name',
                                                    forceSelection: true,
                                                    value: '1'
                                                }
                                            ]
                                        }
                                    ]
                                }
                            ]
                        }
                    ]
                });
                eshopOrderDetailWin.show();
//                detailWin.showAt(20, 90);

            } else {
                eshopOrderDetailWin.hide(null, function() {

                });
            }
        }
    },
            
    onEshopOrderPacketa: function (evtData) {

        var eshopOrderStore = this.getStore('Eshop.stores.EshopOrders');
        var record = eshopOrderStore.getAt(evtData.rowIndex);

        
        if(record) {
            if (!eshopOrderDetailWin) {
                var eshopOrderDetailWin = Ext.create('widget.window', {
                    title: 'Objednávka prepravy Packeta pre obj. ' + record.get('number') ,
                    closable: true,
                    closeAction: 'destroy',
                    width: 780,
                    minWidth: 430,
                    height: 480,
                    modal: true,
                    autoScroll: true,
                    listeners: {
                        show: function() {
                            Ext.Ajax.request({
                                url: '/mvc/Eshop/EshopOrders/admin_loadWithItemsForPacketa?lang=' + runConfig.lang,
                                params: {
                                    orderId: record.data.id
                                },
                                success: function (response) {
                                    var jsonResponse = Ext.decode(response.responseText);
                                    eshopOrderDetailWin.down('form').getForm().setValues(jsonResponse.data);
                                    
                                    // set address fieldset title 
                                    var addressFieldset = Ext.getCmp('packeta-address-fieldset');
                                    var addressFieldserTitle = __jsa('Eshop', 'Príjemca');
                                    if (jsonResponse.data.pickup_place_id) {
                                        addressFieldserTitle = __jsa('Eshop', 'Kontakt') + '<span class="field-info" data-qtip="' + __jsa('Eshop', 'Kontakt na osobu, ktorá vytvorila objednávku.') + '">i</span>';
                                    }
                                    addressFieldset.setTitle(addressFieldserTitle);
                                    
                                    var message = jsonResponse.message;
                                    if (message) {
                                        if (jsonResponse.success) {
                                            message = '<img style="vertical-align: top; width: 16px;" src="/img/silk/accept.png" alt="OK" /> ' + message;
                                        } else {
                                            message = '<img style="vertical-align: top; width: 16px;" src="/img/silk/exclamation.png" alt="OK" /> ' + message;
                                        }
                                        eshopOrderDetailWin.down('#PacketaApiAnswer').update(message);
                                    }

                                    if (jsonResponse.data.shipment_numbers) {
                                        eshopOrderDetailWin.down('#many-packages-checkbox').hide();
                                    }
                                }
                            });
                        },
                        close: function() {
                            var ordersGrid = Ext.getCmp('eshopeshopOrdersgrid');
                            var selectionModel = ordersGrid.getSelectionModel();
                            var selectedRecordId = null;
                            if (selectionModel.hasSelection()) {
                                var selectedRecordId = selectionModel.getSelection()[0].getId();
                            }
                            ordersGrid.getView().saveScrollState();
                            ordersGrid.getStore().load({
                                callback: function() {
                                    ordersGrid.getView().restoreScrollState();
                                    // retore selection - find the previously selected
                                    // record by its id and if it is still present in grid
                                    // then select it back after the grid reload
                                    var selectedRecord;
                                    if (
                                        selectedRecordId
                                        && (selectedRecord = ordersGrid.getStore().getById(selectedRecordId))
                                    ) {
                                        selectionModel.select(selectedRecord);
                                    }
                                }
                            });
                        }
                    },
                    bodyPadding: 10,
                    dockedItems: {
                        xtype: 'toolbar',
                        border: false,
                        style: {
                            background: '#DFE8F6',
                            border: 0,
                            margin: 0
                        },
                        padding: '8 8 6 8',
                        dock: 'bottom',
                        layout : {
                            type : 'hbox',
                            pack : 'center'
                        },
                        items: [{
                            xtype: 'button',
                            iconCls: 'accept',
                            id: 'orderPacketaButton',
                            text: 'Objednať prepravu Packeta',
                            handler: function() {
                                var data = eshopOrderDetailWin.down('form').getForm().getFieldValues();
                                Ext.Ajax.request({
                                    url: '/mvc/Eshop/EshopOrders/admin_orderPacketa?lang=' + runConfig.lang,
                                    params: {
                                        data: Ext.encode(data)
                                    },
                                    success: function (response) {
                                        eshopOrderDetailWin.enable();
                                        var jsonResponse = Ext.decode(response.responseText);
                                        var message = jsonResponse.message;
                                        if (jsonResponse.success) {
                                            message = '<img style="vertical-align: top; width: 16px;" src="/img/silk/accept.png" alt="OK" /> ' + message;
                                            eshopOrderDetailWin.down('#orderPacketaButton').disable();
                                        } else {
                                            message = '<img style="vertical-align: top; width: 16px;" src="/img/silk/exclamation.png" alt="OK" /> ' + message;
                                        }
                                        eshopOrderDetailWin.down('#PacketaApiAnswer').update(message);
                                    }
                                });
                                eshopOrderDetailWin.disable();
                            }
                        }]
                    },
                    items: [
                        {
                            xtype: 'container',
                            id: 'PacketaApiAnswer',
                            html: '',
                            style: {
                                background: '#f1f6a5',
                                padding: '15px',
                                border: '1px solid #b5b8c8',
                                margin: '0 0 10px 0'
                            },
                        },
                        {
                            xtype: 'form',
                            layout: {
                                type: 'column',
                                padding: 5
                            },
                            bodyStyle: {
                                background: '#DFE8F6'
                            },
                            border: false,
                            cls: 'eshopOrderDetailForm',
                            items: [
                                {
                                    xtype: 'container',
                                    items: [{
                                        xtype: 'fieldset',
                                        id: 'packeta-address-fieldset',
                                        title: '&nbsp;', // title is set dynamically here above
                                        padding: '0 10 0 10',
                                        margin: '0 0 10 0',
                                        defaults: {
                                            width: 300
                                        },
                                        items: [
                                            {
                                                xtype: 'hidden',
                                                name: 'id'
                                            }, {
                                                xtype: 'textfield',
                                                name: 'first_name',
                                                fieldLabel: 'Meno príjemcu'
                                            }, {
                                                xtype: 'textfield',
                                                name: 'last_name',
                                                fieldLabel: 'Priezvisko príjemcu'
                                            }, {
                                                xtype: 'textfield',
                                                name: 'street',
                                                fieldLabel: 'Ulica'
                                            }, {
                                                xtype: 'textfield',
                                                name: 'house_number',
                                                fieldLabel: 'Číslo domu'
                                            }, {
                                                xtype: 'textfield',
                                                name: 'city',
                                                fieldLabel: 'Mesto'
                                            }, {
                                                xtype: 'textfield',
                                                name: 'zip',
                                                fieldLabel: 'PSČ'
                                            }, {
                                                xtype: 'combo',
                                                forceSelection: true,
                                                queryMode: 'local',
                                                typeAhead: true,
                                                name: 'country',
                                                fieldLabel: 'Štát',
                                                valueField: 'pid',
                                                displayField: 'label',
                                                triggerAction: 'all',
                                                mode: 'local',
                                                store: new Ext.data.Store({
                                                    fields: ['pid', 'label'],
                                                    data: runConfig.countries
                                                })
                                            }, {
                                                xtype: 'textfield',
                                                name: 'phone',
                                                fieldLabel: __jsa('Eshop', 'Phone'),
                                                afterLabelTextTpl: '<span class="field-info" data-qtip="' + __jsa('Eshop', 'Je nutné vyplniť minimálne jeden z údajov e-mail/telefón') + '">i</span>'
//                                            }, {
//                                                xtype: 'textfield',
//                                                name: 'contact_person',
//                                                fieldLabel: 'Kont. osoba'
                                            }, {
                                                xtype: 'textfield',
                                                name: 'email',
                                                fieldLabel: __jsa('Eshop', 'E-mail'),
                                                afterLabelTextTpl: '<span class="field-info" data-qtip="' + __jsa('Eshop', 'Je nutné vyplniť minimálne jeden z údajov e-mail/telefón') + '">i</span>'
//                                            }, {
//                                                xtype: 'textarea',
//                                                name: 'comment',
//                                                fieldLabel: __jsa('Eshop', 'Comment')
                                            }
                                        ]
                                    }]
                                }, {
                                    xtype: 'container',
                                    padding: '0 0 0 20',
                                    items: [
                                        {
                                            xtype: 'fieldset',
                                            title: 'Zásielka',
                                            padding: '0 10 0 10',
                                            margin: '0 0 10 0',
                                            defaults: {
                                                labelWidth: 160,
                                                width: 350
                                            },
                                            items: [
                                                {
                                                    xtype: 'textfield',
                                                    name: 'pickup_place_id',
                                                    fieldLabel: 'ID odberného miesta',
                                                    afterLabelTextTpl: '<span class="field-info" data-qtip="' + __jsa('Eshop', 'ID odberného miesta. Ak obsahuje dvojbodku (&quot;:&quot;) tak ide o identifikátor odberného miesta externého dopravcu vo forme &quot;{idExternehoDopravcu}:{idExternehoOdbernehoMiesta}&quot;.') + '">i</span>'
                                                }, {
                                                    xtype: 'textfield',
                                                    name: 'value',
                                                    fieldLabel: 'Hodnota poistenia',
                                                    afterLabelTextTpl: '<span class="field-info" data-qtip="' + __jsa('Eshop', 'Poistná hodnota zásielky') + '">i</span>'
                                                }, {
                                                    xtype: 'checkboxfield',
                                                    id: 'many-packages-checkbox',
                                                    name: 'many_packages',
                                                    fieldLabel: 'Viac balíkov',
                                                    afterLabelTextTpl: '<span class="field-info" data-qtip="' + __jsa('Eshop', 'Zaškrtnite, ak sa objednávka rozdelí na viacero balíkov. Stačí to zaškrtnúť pri prvom balíku. V e-mailovej správe odoslanej objednávky sa následne pripojí info, že objednávka je rozdelená do viacerých balíkov.') + '">i</span>'
                                                }, {
                                                    xtype: 'textfield',
                                                    name: 'weight',
                                                    fieldLabel: 'Hmotnosť [kg]',
                                                    afterLabelTextTpl: '<span class="field-info" data-qtip="' + __jsa('Eshop', 'Max povolená hmotnosť balíka je 10 kg') + '">i</span>'
//                                                }, {
//                                                    xtype: 'textfield',
//                                                    name: 'package_count',
//                                                    fieldLabel: 'Počet kusov (balíkov)',
//                                                    value: '1'
                                                }, {
                                                    xtype: 'textfield',
                                                    name: 'reference',
                                                    fieldLabel: 'Referencia',
                                                    afterLabelTextTpl: '<span class="field-info" data-qtip="' + __jsa('Eshop', 'Označenie zásielky vo vašom obchode, číslo objednávky') + '">i</span>'
                                                }, {
                                                    xtype: 'textfield',
                                                    name: 'dobierka',
                                                    fieldLabel: 'Dobierka'
//                                                }, {
//                                                    xtype: 'textfield',
//                                                    name: 'dobierka_cislo_uctu',
//                                                    fieldLabel: 'Číslo účtu pre dobierku'
//                                                }, {
//                                                    xtype: 'textfield',
//                                                    name: 'dobierka_vs',
//                                                    fieldLabel: 'Variabilný symbol dobierka'
                                                }, {
                                                    xtype: 'textarea',
                                                    name: 'description',
                                                    fieldLabel: 'Popis zásielky',
                                                    afterLabelTextTpl: '<span class="field-info" data-qtip="' + __jsa('Eshop', 'Poznámka na etikete balíka viditeľná pre vodiča aj pre príjemcu. POZOR: Vytlačí sa len prvých 32 znakov!') + '">i</span>'
//                                                }, {
//                                                    xtype: 'combo',
//                                                    fieldLabel: 'SMS notifikácia',
//                                                    name: 'sms_notification',
//                                                    store: Ext.create('Ext.data.Store', {
//                                                        fields: ['id', 'name'],
//                                                        data : [
//                                                            {'id': '1', 'name': 'Áno'},
//                                                            {'id': '', 'name': 'Nie'}
//                                                        ]
//                                                    }),
//                                                    valueField: 'id',
//                                                    displayField: 'name',
//                                                    forceSelection: true,
//                                                    value: '1'
                                                }
                                            ]
                                        }, {
                                            xtype: 'fieldset',
                                            title: __jsa('Eshop', 'Čísla odoslaných zásielok'),
                                            padding: '0 10 0 10',
                                            margin: '0 0 10 0',
                                            defaults: {
                                                width: 350
                                            },
                                            items: [{
                                                xtype: 'textfield',
                                                name: 'shipment_numbers',
                                                readOnly: true,
                                                disabled: true
                                            }]
                                        }
                                    ]
                                }
                            ]
                        }
                    ]
                });
                eshopOrderDetailWin.show();
//                detailWin.showAt(20, 90);

            } else {
                eshopOrderDetailWin.hide(null, function() {

                });
            }
        }
    },
            
    onEshopOrderSps: function (evtData) {

        var eshopOrderStore = this.getStore('Eshop.stores.EshopOrders');
        var record = eshopOrderStore.getAt(evtData.rowIndex);

        
        if(record) {
            if (!eshopOrderDetailWin) {
                var eshopOrderDetailWin = Ext.create('widget.window', {
                    title: 'Objednávka prepravy Slovak Parcel Service pre obj. ' + record.get('number') ,
                    closable: true,
                    closeAction: 'destroy',
                    width: 780,
                    minWidth: 430,
                    height: 480,
                    modal: true,
                    autoScroll: true,
                    listeners: {
                        show: function() {
                            Ext.Ajax.request({
                                url: '/mvc/Eshop/EshopOrders/admin_loadWithItemsForSps?lang=' + runConfig.lang,
                                params: {
                                    orderId: record.data.id
                                },
                                success: function (response) {
                                    var jsonResponse = Ext.decode(response.responseText);
                                    eshopOrderDetailWin.down('form').getForm().setValues(jsonResponse.data);
                                    var message = jsonResponse.message;
                                    if (message) {
                                        if (jsonResponse.success) {
                                            message = '<img style="vertical-align: top; width: 16px;" src="/img/silk/accept.png" alt="OK" /> ' + message;
                                        } else {
                                            message = '<img style="vertical-align: top; width: 16px;" src="/img/silk/exclamation.png" alt="OK" /> ' + message;
                                        }
                                        eshopOrderDetailWin.down('#SpsApiAnswer').update(message);
                                    }
                                }
                            });
                        },
                        close: function() {
                            var ordersGrid = Ext.getCmp('eshopeshopOrdersgrid');
                            var selectionModel = ordersGrid.getSelectionModel();
                            var selectedRecordId = null;
                            if (selectionModel.hasSelection()) {
                                var selectedRecordId = selectionModel.getSelection()[0].getId();
                            }
                            ordersGrid.getView().saveScrollState();
                            ordersGrid.getStore().load({
                                callback: function() {
                                    ordersGrid.getView().restoreScrollState();
                                    // retore selection - find the previously selected
                                    // record by its id and if it is still present in grid
                                    // then select it back after the grid reload
                                    var selectedRecord;
                                    if (
                                        selectedRecordId
                                        && (selectedRecord = ordersGrid.getStore().getById(selectedRecordId))
                                    ) {
                                        selectionModel.select(selectedRecord);
                                    }
                                }
                            });
                        }
                    },
                    bodyPadding: 10,
                    dockedItems: {
                        xtype: 'toolbar',
                        border: false,
                        style: {
                            background: '#DFE8F6',
                            border: 0,
                            margin: 0
                        },
                        padding: '8 8 6 8',
                        dock: 'bottom',
                        layout : {
                            type : 'hbox',
                            pack : 'center'
                        },
                        items: [{
                            xtype: 'button',
                            iconCls: 'accept',
                            id: 'orderSpsButton',
                            text: 'Objednať prepravu SPS',
                            handler: function() {
                                var data = eshopOrderDetailWin.down('form').getForm().getFieldValues();
                                Ext.Ajax.request({
                                    url: '/mvc/Eshop/EshopOrders/admin_orderSps?lang=' + runConfig.lang,
                                    params: {
                                        data: Ext.encode(data)
                                    },
                                    success: function (response) {
                                        eshopOrderDetailWin.enable();
                                        var jsonResponse = Ext.decode(response.responseText);
                                        var message = jsonResponse.message;
                                        if (jsonResponse.success) {
                                            message = '<img style="vertical-align: top; width: 16px;" src="/img/silk/accept.png" alt="OK" /> ' + message;
                                            eshopOrderDetailWin.down('#orderSpsButton').disable();
                                        } else {
                                            message = '<img style="vertical-align: top; width: 16px;" src="/img/silk/exclamation.png" alt="OK" /> ' + message;
                                        }
                                        eshopOrderDetailWin.down('#SpsApiAnswer').update(message);
                                    }
                                });
                                eshopOrderDetailWin.disable();
                            }
                        }]
                    },
                    items: [
                        {
                            xtype: 'container',
                            id: 'SpsApiAnswer',
                            html: '',
                            style: {
                                background: '#f1f6a5',
                                padding: '15px',
                                border: '1px solid #b5b8c8',
                                margin: '0 0 10px 0'
                            },
                        },
                        {
                            xtype: 'form',
                            layout: {
                                type: 'column',
                                padding: 5
                            },
                            bodyStyle: {
                                background: '#DFE8F6'
                            },
                            border: false,
                            cls: 'eshopOrderDetailForm',
                            items: [
                                {
                                    xtype: 'container',
                                    items: [{
                                        xtype: 'fieldset',
                                        title: 'Príjemca',
                                        padding: '0 10 0 10',
                                        margin: '0 0 10 0',
                                        defaults: {
                                            width: 300
                                        },
                                        items: [
                                            {
                                                xtype: 'hidden',
                                                name: 'id'
                                            }, {
                                                xtype: 'textfield',
                                                name: 'company_name',
                                                fieldLabel: 'Názov firmy'
                                            }, {
                                                xtype: 'textfield',
                                                name: 'fullname',
                                                fieldLabel: 'Príjemca'
                                            }, {
                                                xtype: 'textfield',
                                                name: 'street',
                                                fieldLabel: 'Ulica, číslo'
                                            }, {
                                                xtype: 'textfield',
                                                name: 'city',
                                                fieldLabel: 'Mesto'
                                            }, {
                                                xtype: 'textfield',
                                                name: 'zip',
                                                fieldLabel: 'PSČ'
                                            }, {
                                                xtype: 'combo',
                                                forceSelection: true,
                                                queryMode: 'local',
                                                typeAhead: true,
                                                name: 'country',
                                                fieldLabel: 'Štát',
                                                valueField: 'pid',
                                                displayField: 'label',
                                                triggerAction: 'all',
                                                mode: 'local',
                                                store: new Ext.data.Store({
                                                    fields: ['pid', 'label'],
                                                    data: runConfig.countries
                                                })
                                            }, {
                                                xtype: 'textfield',
                                                name: 'phone',
                                                fieldLabel: __jsa('Eshop', 'Phone'),
                                                afterLabelTextTpl: '<span class="field-info" data-qtip="' + __jsa('Eshop', 'Je nutné vyplniť minimálne jeden z údajov e-mail/telefón') + '">i</span>'
//                                            }, {
//                                                xtype: 'textfield',
//                                                name: 'contact_person',
//                                                fieldLabel: 'Kont. osoba'
                                            }, {
                                                xtype: 'textfield',
                                                name: 'email',
                                                fieldLabel: __jsa('Eshop', 'E-mail'),
                                                afterLabelTextTpl: '<span class="field-info" data-qtip="' + __jsa('Eshop', 'Je nutné vyplniť minimálne jeden z údajov e-mail/telefón') + '">i</span>'
//                                            }, {
//                                                xtype: 'textarea',
//                                                name: 'comment',
//                                                fieldLabel: __jsa('Eshop', 'Comment')
                                            }
                                        ]
                                    }]
                                }, {
                                    xtype: 'container',
                                    padding: '0 0 0 20',
                                    items: [
                                        {
                                            xtype: 'fieldset',
                                            title: 'Zásielka',
                                            padding: '0 10 0 10',
                                            margin: '0 0 10 0',
                                            defaults: {
                                                labelWidth: 160,
                                                width: 350
                                            },
                                            items: [
                                                {
                                                    xtype: 'textfield',
                                                    name: 'value',
                                                    fieldLabel: 'Hodnota poistenia',
                                                    afterLabelTextTpl: '<span class="field-info" data-qtip="' + __jsa('Eshop', 'Poistná hodnota zásielky') + '">i</span>'
                                                }, {
                                                    xtype: 'textfield',
                                                    name: 'package_count',
                                                    fieldLabel: 'Počet balíkov',
                                                    value: '1'
                                                }, {
                                                    xtype: 'textfield',
                                                    name: 'weight',
                                                    fieldLabel: 'Hmotnosti balíkov [kg]',
                                                    afterLabelTextTpl: '<span class="field-info" data-qtip="' + __jsa('Eshop', 'Ak je viacej balíkov, tak jednotlivé hmotnosti oddeľte medzerou, napr. pre tri balíky &quot;1,5 2 1&quot;. Max povolená hmotnosť balíka do zahraničia je 10 kg') + '">i</span>',
                                                    value: '1'
                                                }, {
                                                    xtype: 'textfield',
                                                    name: 'reference',
                                                    fieldLabel: 'Referencia',
                                                    afterLabelTextTpl: '<span class="field-info" data-qtip="' + __jsa('Eshop', 'Označenie zásielky vo vašom obchode, číslo objednávky') + '">i</span>'
                                                }, {
                                                    xtype: 'textfield',
                                                    name: 'dobierka',
                                                    fieldLabel: 'Dobierka'
//                                                }, {
//                                                    xtype: 'textfield',
//                                                    name: 'dobierka_cislo_uctu',
//                                                    fieldLabel: 'Číslo účtu pre dobierku'
//                                                }, {
//                                                    xtype: 'textfield',
//                                                    name: 'dobierka_vs',
//                                                    fieldLabel: 'Variabilný symbol dobierka'
                                                }, {
                                                    xtype: 'textarea',
                                                    name: 'description',
                                                    fieldLabel: 'Popis zásielky',
                                                    afterLabelTextTpl: '<span class="field-info" data-qtip="' + __jsa('Eshop', 'Poznámka na etikete balíka viditeľná pre vodiča aj pre príjemcu. POZOR: Vytlačí sa len prvých 32 znakov!') + '">i</span>'
//                                                }, {
//                                                    xtype: 'combo',
//                                                    fieldLabel: 'SMS notifikácia',
//                                                    name: 'sms_notification',
//                                                    store: Ext.create('Ext.data.Store', {
//                                                        fields: ['id', 'name'],
//                                                        data : [
//                                                            {'id': '1', 'name': 'Áno'},
//                                                            {'id': '', 'name': 'Nie'}
//                                                        ]
//                                                    }),
//                                                    valueField: 'id',
//                                                    displayField: 'name',
//                                                    forceSelection: true,
//                                                    value: '1'
                                                }
                                            ]
                                        }
                                    ]
                                }
                            ]
                        }
                    ]
                });
                eshopOrderDetailWin.show();
//                detailWin.showAt(20, 90);

            } else {
                eshopOrderDetailWin.hide(null, function() {

                });
            }
        }
    },
    onEshopOrderDpd: function (evtData) {

        var eshopOrderStore = this.getStore('Eshop.stores.EshopOrders');
        var record = eshopOrderStore.getAt(evtData.rowIndex);

        
        if(record) {
            if (!eshopOrderDetailWin) {
                var eshopOrderDetailWin = Ext.create('widget.window', {
                    title: 'Objednávka prepravy DPD pre obj. ' + record.get('number') ,
                    closable: true,
                    closeAction: 'destroy',
                    width: 780,
                    minWidth: 430,
                    height: 480,
                    modal: true,
                    autoScroll: true,
                    listeners: {
                        show: function() {
                            Ext.Ajax.request({
                                url: '/mvc/Eshop/EshopOrders/admin_loadWithItemsForDpd?lang=' + runConfig.lang,
                                params: {
                                    orderId: record.data.id
                                },
                                success: function (response) {
                                    var jsonResponse = Ext.decode(response.responseText);
                                    eshopOrderDetailWin.down('form').getForm().setValues(jsonResponse.data);
                                    
                                    // set address fieldset title 
                                    var addressFieldset = Ext.getCmp('dpd-address-fieldset');
                                    var addressFieldserTitle = __jsa('Eshop', 'Príjemca');
                                    if (jsonResponse.data.pickup_place_id) {
                                        addressFieldserTitle = __jsa('Eshop', 'Kontakt') + '<span class="field-info" data-qtip="' + __jsa('Eshop', 'Kontakt na osobu, ktorá vytvorila objednávku.') + '">i</span>';
                                    }
                                    addressFieldset.setTitle(addressFieldserTitle);
                                    
                                    var message = jsonResponse.message;
                                    if (message) {
                                        if (jsonResponse.success) {
                                            message = '<img style="vertical-align: top; width: 16px;" src="/img/silk/accept.png" alt="OK" /> ' + message;
                                        } else {
                                            message = '<img style="vertical-align: top; width: 16px;" src="/img/silk/exclamation.png" alt="OK" /> ' + message;
                                        }
                                        eshopOrderDetailWin.down('#DpdApiAnswer').update(message);
                                    }
                                    if (jsonResponse.data.shipment_numbers) {
                                        eshopOrderDetailWin.down('#many-packages-checkbox').hide();
                                    }
                                }
                            });
                        },
                        close: function() {
                            var ordersGrid = Ext.getCmp('eshopeshopOrdersgrid');
                            var selectionModel = ordersGrid.getSelectionModel();
                            var selectedRecordId = null;
                            if (selectionModel.hasSelection()) {
                                var selectedRecordId = selectionModel.getSelection()[0].getId();
                            }
                            ordersGrid.getView().saveScrollState();
                            ordersGrid.getStore().load({
                                callback: function() {
                                    ordersGrid.getView().restoreScrollState();
                                    // retore selection - find the previously selected
                                    // record by its id and if it is still present in grid
                                    // then select it back after the grid reload
                                    var selectedRecord;
                                    if (
                                        selectedRecordId
                                        && (selectedRecord = ordersGrid.getStore().getById(selectedRecordId))
                                    ) {
                                        selectionModel.select(selectedRecord);
                                    }
                                }
                            });
                        }
                    },
                    bodyPadding: 10,
                    dockedItems: {
                        xtype: 'toolbar',
                        border: false,
                        style: {
                            background: '#DFE8F6',
                            border: 0,
                            margin: 0
                        },
                        padding: '8 8 6 8',
                        dock: 'bottom',
                        layout : {
                            type : 'hbox',
                            pack : 'center'
                        },
                        items: [{
                            xtype: 'button',
                            iconCls: 'accept',
                            id: 'orderDpdButton',
                            text: 'Objednať prepravu DPD',
                            handler: function() {
                                var data = eshopOrderDetailWin.down('form').getForm().getFieldValues();
                                Ext.Ajax.request({
                                    url: '/mvc/Eshop/EshopOrders/admin_orderDpd?lang=' + runConfig.lang,
                                    params: {
                                        data: Ext.encode(data)
                                    },
                                    success: function (response) {
                                        eshopOrderDetailWin.enable();
                                        var jsonResponse = Ext.decode(response.responseText);
                                        var message = jsonResponse.message;
                                        if (jsonResponse.success) {
                                            message = '<img style="vertical-align: top; width: 16px;" src="/img/silk/accept.png" alt="OK" /> ' + message;
                                            eshopOrderDetailWin.down('#orderDpdButton').disable();
                                        } else {
                                            message = '<img style="vertical-align: top; width: 16px;" src="/img/silk/exclamation.png" alt="OK" /> ' + message;
                                        }
                                        eshopOrderDetailWin.down('#DpdApiAnswer').update(message);
                                    }
                                });
                                eshopOrderDetailWin.disable();
                            }
                        }]
                    },
                    items: [{
                        xtype: 'container',
                        id: 'DpdApiAnswer',
                        html: '',
                        style: {
                            background: '#f1f6a5',
                            padding: '15px',
                            border: '1px solid #b5b8c8',
                            margin: '0 0 10px 0'
                        }
                    }, {
                        xtype: 'form',
                        layout: {
                            type: 'column',
                            padding: 5
                        },
                        bodyStyle: {
                            background: '#DFE8F6'
                        },
                        border: false,
                        cls: 'eshopOrderDetailForm',
                        items: [{
                            xtype: 'container',
                            items: [{
                                xtype: 'fieldset',
                                title: __jsa('Eshop', 'Odberné miesto') + '<span class="field-info" data-qtip="' + __jsa('Eshop', 'Je vyplnené v prípade, že sa využila služba &quot;Vyzdvihnutie na odbernom mieste&quot;.') + '">i</span>',
                                padding: '0 10 0 10',
                                margin: '0 0 10 0',
                                defaults: {
                                    width: 300
                                },
                                items: [{
                                    xtype: 'textfield',
                                    name: 'pickup_place_id',
                                    fieldLabel: 'Číslo'
                                }, {
                                    xtype: 'textfield',
                                    name: 'pickup_place',
                                    fieldLabel: 'Adresa'
                                }]
                            }, {
                                xtype: 'fieldset',
                                id: 'dpd-address-fieldset',
                                title: '&nbsp;', // title is set dynamically here above
                                padding: '0 10 0 10',
                                margin: '0 0 10 0',
                                defaults: {
                                    width: 300
                                },
                                items: [{
                                    xtype: 'hidden',
                                    name: 'id'
                                }, {
                                    xtype: 'textfield',
                                    name: 'company_name',
                                    fieldLabel: 'Názov firmy'
                                }, {
                                    xtype: 'textfield',
                                    name: 'fullname',
                                    fieldLabel: 'Príjemca'
                                }, {
                                    xtype: 'textfield',
                                    name: 'street',
                                    fieldLabel: 'Ulica'
                                }, {
                                    xtype: 'textfield',
                                    name: 'house_number',
                                    fieldLabel: 'Číslo'
                                }, {
                                    xtype: 'textfield',
                                    name: 'city',
                                    fieldLabel: 'Mesto'
                                }, {
                                    xtype: 'textfield',
                                    name: 'zip',
                                    fieldLabel: 'PSČ'
                                }, {
                                    xtype: 'combo',
                                    forceSelection: true,
                                    queryMode: 'local',
                                    typeAhead: true,
                                    name: 'country',
                                    fieldLabel: 'Štát',
                                    valueField: 'pid',
                                    displayField: 'label',
                                    triggerAction: 'all',
                                    mode: 'local',
                                    store: new Ext.data.Store({
                                        fields: ['pid', 'label'],
                                        data: runConfig.countries
                                    })
                                }, {
                                    xtype: 'textfield',
                                    name: 'phone',
                                    fieldLabel: __jsa('Eshop', 'Phone'),
                                    afterLabelTextTpl: '<span class="field-info" data-qtip="' + __jsa('Eshop', 'Je nutné vyplniť minimálne jeden z údajov e-mail/telefón') + '">i</span>'
//                                            }, {
//                                                xtype: 'textfield',
//                                                name: 'contact_person',
//                                                fieldLabel: 'Kont. osoba'
                                }, {
                                    xtype: 'textfield',
                                    name: 'email',
                                    fieldLabel: __jsa('Eshop', 'E-mail'),
                                    afterLabelTextTpl: '<span class="field-info" data-qtip="' + __jsa('Eshop', 'Je nutné vyplniť minimálne jeden z údajov e-mail/telefón') + '">i</span>'
//                                            }, {
//                                                xtype: 'textarea',
//                                                name: 'comment',
//                                                fieldLabel: __jsa('Eshop', 'Comment')
                                }]
                            }]
                        }, {
                            xtype: 'container',
                            padding: '0 0 0 20',
                            items: [{
                                xtype: 'fieldset',
                                title: __jsa('Eshop', 'Zásielka'),
                                padding: '0 10 0 10',
                                margin: '0 0 10 0',
                                defaults: {
                                    labelWidth: 160,
                                    width: 350
                                },
                                items: [{
                                    /*/> there is fixed insurance (according to DPD product) so there is no need to send some order value
                                    xtype: 'textfield',
                                    name: 'value',
                                    fieldLabel: 'Hodnota poistenia',
                                    afterLabelTextTpl: '<span class="field-info" data-qtip="' + __jsa('Eshop', 'Poistná hodnota zásielky') + '">i</span>'
                                }, {
                                    //*/
                                    xtype: 'textfield',
                                    name: 'pickup_date',
                                    fieldLabel: 'Dátum zvozu'
                                }, {
                                    xtype: 'textfield',
                                    name: 'package_count',
                                    fieldLabel: 'Počet balíkov',
                                    value: '1',
                                    listeners: {
                                        change: function(field, newValue) {
                                            var manyPackagesChekbox = eshopOrderDetailWin.down('#many-packages-checkbox');
                                            var count = parseInt(newValue, 10);
                                            var shipmentNumbersDisplay = eshopOrderDetailWin.down('#shipment-numbers-display');
                                            var shipmentNumbers = shipmentNumbersDisplay ? shipmentNumbersDisplay.getValue() : '';
                                            shipmentNumbers = Ext.String.trim(shipmentNumbers);
                                            if (
                                                !isNaN(count)
                                                && count > 1
                                                ||
                                                shipmentNumbers !== ''
                                            ) {
                                                manyPackagesChekbox.hide();
                                            }
                                            else {
                                                manyPackagesChekbox.show();
                                            }
                                        }
                                    }
                                }, {
                                    xtype: 'checkboxfield',
                                    id: 'many-packages-checkbox',
                                    name: 'many_packages',
                                    fieldLabel: 'Viac balíkov',
                                    afterLabelTextTpl: '<span class="field-info" data-qtip="' + __jsa('Eshop', 'Zaškrtnite, ak sa objednávka rozdelí na viacero balíkov. Stačí to zaškrtnúť pri prvom balíku. V e-mailovej správe odoslanej objednávky sa následne pripojí info, že objednávka je rozdelená do viacerých balíkov.') + '">i</span>'                                
                                }, {
                                    xtype: 'textfield',
                                    name: 'weight',
                                    fieldLabel: 'Hmotnosti balíkov [kg]',
                                    afterLabelTextTpl: '<span class="field-info" data-qtip="' + __jsa('Eshop', 'Ak je viacej balíkov, tak jednotlivé hmotnosti oddeľte medzerou, napr. pre tri balíky &quot;1,5 2 1&quot;. Max povolená hmotnosť balíka do zahraničia je 10 kg') + '">i</span>',
                                    value: '1'
                                }, {
                                    xtype: 'textfield',
                                    name: 'reference',
                                    fieldLabel: 'Referencia',
                                    afterLabelTextTpl: '<span class="field-info" data-qtip="' + __jsa('Eshop', 'Označenie zásielky vo vašom obchode, číslo objednávky') + '">i</span>'
                                }, {
                                    xtype: 'textfield',
                                    name: 'dobierka',
                                    fieldLabel: 'Dobierka'
//                                                }, {
//                                                    xtype: 'textfield',
//                                                    name: 'dobierka_cislo_uctu',
//                                                    fieldLabel: 'Číslo účtu pre dobierku'
//                                                }, {
//                                                    xtype: 'textfield',
//                                                    name: 'dobierka_vs',
//                                                    fieldLabel: 'Variabilný symbol dobierka'
                                }, {
                                    xtype: 'textarea',
                                    name: 'description',
                                    fieldLabel: 'Popis zásielky',
                                    afterLabelTextTpl: '<span class="field-info" data-qtip="' + __jsa('Eshop', 'Poznámka na etikete balíka viditeľná pre vodiča aj pre príjemcu. POZOR: Vytlačí sa len prvých 32 znakov!') + '">i</span>'
                                }, {
                                    xtype: 'combo',
                                    fieldLabel: 'Doručenie v sobotu',
                                    name: 'saturday_delivery',
                                    store: Ext.create('Ext.data.Store', {
                                        fields: ['id', 'name'],
                                        data : [
                                            {'id': '0', 'name': 'Nie'},
                                            {'id': '1', 'name': 'Áno'}
                                        ]
                                    }),
                                    valueField: 'id',
                                    displayField: 'name',
                                    forceSelection: true,
                                    value: '0'
                                }]
                            }, {
                                xtype: 'fieldset',
                                title: __jsa('Eshop', 'Čísla odoslaných zásielok'),
                                padding: '0 10 0 10',
                                margin: '0 0 10 0',
                                defaults: {
                                    width: 350
                                },
                                items: [{
                                    xtype: 'textfield',
                                    id: 'shipment-numbers-display',
                                    name: 'shipment_numbers',
                                    readOnly: true,
                                    disabled: true
                                }]
                            }]
                        }]
                    }]
                });
                eshopOrderDetailWin.show();
//                detailWin.showAt(20, 90);

            } else {
                eshopOrderDetailWin.hide(null, function() {

                });
            }
        }
    },
    onEshopOrderDetailB: function (evtData) {

        var eshopOrderStore = this.getStore('Eshop.stores.EshopOrders');
        var record = eshopOrderStore.getAt(evtData.rowIndex);
        if(record) {
            if (!detailWin) {
                var leftPanelContent = '';
                if (record.get('run_users_id')) leftPanelContent += __jsa('Eshop', 'Id of user') + ': ' + record.get('run_users_id') + '<br>';
                leftPanelContent +=    
                    record.get('delivery_fullname') + '<br>' + 
                    record.get('delivery_street') + '<br>' + 
                    record.get('delivery_zip') + ' ' + record.get('delivery_city') + '<br>' +
                    __jsa('Eshop', record.get('delivery_country')) + '<br><br>' +
                    __jsa('Eshop', 'E-mail') + ': ' + record.get('email') + '<br>' +
                    __jsa('Eshop', 'Tel.') + ': ' + record.get('phone') + '<br><br>' +
                    __jsa('Eshop', 'Shipment method') + ': ' + record.get('shipment_method_name') + ' - ' + formatPrice(parseFloat(record.get('shipment_price')), record.get('merchant_pid')) + '<br>' +
                    __jsa('Eshop', 'Payment method') + ': ' + record.get('payment_method_name') + ' - ' + formatPrice(parseFloat(record.get('payment_price')), record.get('merchant_pid')) + '<br>' +
                    __jsa('Eshop', 'Comment') + ': ' + record.get('comment');
                var detailWin = Ext.create('widget.window', {
                    title: __jsa('Eshop', 'Order details no.') + ' ' + record.get('id') ,
                    closable: true,
                    closeAction: 'destroy',
                    width: 650,
                    minWidth: 350,
                    height: 350,
                    layout: {
                        type: 'border',
                        padding: 5
                    },
                    items: [{
                        region: 'west',
                        title: __jsa('Eshop', 'Buyer'),
                        bodyPadding: 5,
                        width: 250,
                        split: true,
                        html: leftPanelContent
                    }, {
                        region: 'center',
                        title: __jsa('Eshop', 'Items of order'),
                        bodyPadding: 5,
                        autoScroll: true,
                        loader: {
                            url: '/mvc/Eshop/EshopOrderProducts/admin_indexHtmlTable?lang=' + runConfig.lang,
                            renderer: 'html',
                            params: {
                                order_id: record.get('id')
                            },
                            autoLoad: true
                        }
                    }]
                });
                detailWin.showAt(20, 90);
                
            } else {
                detailWin.hide(null, function() {
                    
                });
            }
//            console.log(Ext.query('#eshopeshopOrdersgrid img.x-action-col-' + evtData.rowIndex));
//            button.dom.disabled = true;
//            if (detailWin.isVisible()) {
//                detailWin.hide(null, function() {
////                    button.dom.disabled = false;
//                });
//            } else {
//                detailWin.hide(null, function() {
////                    button.dom.disabled = false;
//                });
//            }
        }
    },
    onEshopOrderWoltDrive: function (evtData) {
        var eshopOrderStore = this.getStore('Eshop.stores.EshopOrders');
        var record = eshopOrderStore.getAt(evtData.rowIndex);

        if(record) {
            if (!eshopOrderDetailWin) {
                var eshopOrderDetailWin = Ext.create('widget.window', {
                    title: 'Objednávka prepravy WoltDrive pre obj. ' + record.get('number'),
                    closable: true,
                    closeAction: 'destroy',
                    width: 780,
                    minWidth: 430,
                    height: 480,
                    modal: true,
                    autoScroll: true,
                    listeners: {
                        show: function() {
                            Ext.Ajax.request({
                                url: '/mvc/Eshop/EshopOrders/admin_loadWithItemsForWoltDrive?lang=' + runConfig.lang,
                                params: {
                                    orderId: record.data.id
                                },
                                success: function (response) {
                                    var jsonResponse = Ext.decode(response.responseText);
                                    eshopOrderDetailWin.down('form').getForm().setValues(jsonResponse.data);
                                    
                                    var message = jsonResponse.message;
                                    if (message) {
                                        if (jsonResponse.success) {
                                            message = '<img style="vertical-align: top; width: 16px;" src="/img/silk/accept.png" alt="OK" /> ' + message;
                                        } else {
                                            message = '<img style="vertical-align: top; width: 16px;" src="/img/silk/exclamation.png" alt="OK" /> ' + message;
                                        }
                                        eshopOrderDetailWin.down('#WoltDriveApiAnswer').update(message);
                                    }
                                }
                            });
                        },
                        close: function() {
                            var ordersGrid = Ext.getCmp('eshopeshopOrdersgrid');
                            var selectionModel = ordersGrid.getSelectionModel();
                            var selectedRecordId = null;
                            if (selectionModel.hasSelection()) {
                                var selectedRecordId = selectionModel.getSelection()[0].getId();
                            }
                            ordersGrid.getView().saveScrollState();
                            ordersGrid.getStore().load({
                                callback: function() {
                                    ordersGrid.getView().restoreScrollState();
                                    var selectedRecord;
                                    if (
                                        selectedRecordId
                                        && (selectedRecord = ordersGrid.getStore().getById(selectedRecordId))
                                    ) {
                                        selectionModel.select(selectedRecord);
                                    }
                                }
                            });
                        }
                    },
                    bodyPadding: 10,
                    dockedItems: {
                        xtype: 'toolbar',
                        border: false,
                        style: {
                            background: '#DFE8F6',
                            border: 0,
                            margin: 0
                        },
                        padding: '8 8 6 8',
                        dock: 'bottom',
                        layout : {
                            type : 'hbox',
                            pack : 'center'
                        },
                        items: [{
                            xtype: 'button',
                            iconCls: 'accept',
                            id: 'orderWoltDriveButton',
                            text: 'Objednať prepravu WoltDrive',
                            handler: function() {
                                var data = eshopOrderDetailWin.down('form').getForm().getFieldValues();
                                Ext.Ajax.request({
                                    url: '/mvc/Eshop/EshopOrders/admin_orderWoltDrive?lang=' + runConfig.lang,
                                    params: {
                                        data: Ext.encode(data)
                                    },
                                    success: function (response) {
                                        eshopOrderDetailWin.enable();
                                        var jsonResponse = Ext.decode(response.responseText);
                                        var message = jsonResponse.message;
                                        if (jsonResponse.success) {
                                            message = '<img style="vertical-align: top; width: 16px;" src="/img/silk/accept.png" alt="OK" /> ' + message;
                                            eshopOrderDetailWin.down('#orderWoltDriveButton').disable();
                                        } else {
                                            message = '<img style="vertical-align: top; width: 16px;" src="/img/silk/exclamation.png" alt="OK" /> ' + message;
                                        }
                                        eshopOrderDetailWin.down('#WoltDriveApiAnswer').update(message);
                                    }
                                });
                                eshopOrderDetailWin.disable();
                            }
                        }]
                    },
                    items: [{
                        xtype: 'container',
                        id: 'WoltDriveApiAnswer',
                        html: '',
                        style: {
                            background: '#f1f6a5',
                            padding: '15px',
                            border: '1px solid #b5b8c8',
                            margin: '0 0 10px 0'
                        }
                    }, {
                        xtype: 'form',
                        layout: {
                            type: 'column',
                            padding: 5
                        },
                        bodyStyle: {
                            background: '#DFE8F6'
                        },
                        border: false,
                        cls: 'eshopOrderDetailForm',
                        items: [{
                            xtype: 'container',
                            items: [{
                                xtype: 'fieldset',
                                title: __jsa('Eshop', 'Príjemca'),
                                padding: '0 10 0 10',
                                margin: '0 0 10 0',
                                defaults: {
                                    width: 300
                                },
                                items: [{
                                    xtype: 'hidden',
                                    name: 'id'
                                }, {
                                    xtype: 'textfield',
                                    name: 'fullname',
                                    fieldLabel: 'Príjemca'
                                }, {
                                    xtype: 'textfield',
                                    name: 'street',
                                    fieldLabel: 'Ulica, číslo'
                                }, {
                                    xtype: 'textfield',
                                    name: 'city',
                                    fieldLabel: 'Mesto'
                                }, {
                                    xtype: 'textfield',
                                    name: 'zip',
                                    fieldLabel: 'PSČ'
                                }, {
                                    xtype: 'textfield',
                                    name: 'phone',
                                    fieldLabel: 'Telefón'
                                }, {
                                    xtype: 'textfield',
                                    name: 'email',
                                    fieldLabel: 'E-mail'
                                }]
                            }]
                        }, {
                            xtype: 'container',
                            padding: '0 0 0 20',
                            items: [{
                                xtype: 'fieldset',
                                title: __jsa('Eshop', 'Zásielka'),
                                padding: '0 10 0 10',
                                margin: '0 0 10 0',
                                defaults: {
                                    width: 300
                                },
                                items: [{
                                    xtype: 'textfield',
                                    name: 'package_count',
                                    fieldLabel: 'Počet balíkov',
                                    value: '1',
                                    listeners: {
                                        change: function(field, newValue) {
                                            var manyPackagesChekbox = eshopOrderDetailWin.down('#many-packages-checkbox');
                                            var count = parseInt(newValue, 10);
                                            var shipmentNumbersDisplay = eshopOrderDetailWin.down('#shipment-numbers-display');
                                            var shipmentNumbers = shipmentNumbersDisplay ? shipmentNumbersDisplay.getValue() : '';
                                            shipmentNumbers = Ext.String.trim(shipmentNumbers);
                                            if (
                                                !isNaN(count)
                                                && count > 1
                                                ||
                                                shipmentNumbers !== ''
                                            ) {
                                                manyPackagesChekbox.hide();
                                            }
                                            else {
                                                manyPackagesChekbox.show();
                                            }
                                        }
                                    }
                                }, {
                                    xtype: 'textfield',
                                    name: 'weight',
                                    fieldLabel: 'Hmotnosti balíkov [kg]',
                                    afterLabelTextTpl: '<span class="field-info" data-qtip="' + __jsa('Eshop', 'Ak je viacej balíkov, tak jednotlivé hmotnosti oddeľte medzerou, napr. pre tri balíky &quot;1,5 2 1&quot;.') + '">i</span>',
                                    value: '1'
                                }, {
                                    xtype: 'textfield',
                                    name: 'reference',
                                    fieldLabel: 'Referencia',
                                    afterLabelTextTpl: '<span class="field-info" data-qtip="' + __jsa('Eshop', 'Označenie zásielky vo vašom obchode, číslo objednávky') + '">i</span>'
                                }, {
                                    xtype: 'textarea',
                                    name: 'description',
                                    fieldLabel: 'Popis zásielky',
                                    afterLabelTextTpl: '<span class="field-info" data-qtip="' + __jsa('Eshop', 'Poznámka na etikete balíka viditeľná pre vodiča aj pre príjemcu.') + '">i</span>'
                                }]
                            }, {
                                xtype: 'fieldset',
                                title: __jsa('Eshop', 'Čísla odoslaných zásielok'),
                                padding: '0 10 0 10',
                                margin: '0 0 10 0',
                                defaults: {
                                    width: 350
                                },
                                items: [{
                                    xtype: 'textfield',
                                    id: 'shipment-numbers-display',
                                    name: 'shipment_numbers',
                                    readOnly: true,
                                    disabled: true
                                }]
                            }]
                        }]
                    }]
                });
                eshopOrderDetailWin.show();
            } else {
                eshopOrderDetailWin.hide(null, function() {

                });
            }
        }
    },
    onEshopOrderDelete: function (evtData) {
        var eshopOrderStore = this.getStore('Eshop.stores.EshopOrders');
        var record = eshopOrderStore.getAt(evtData.rowIndex);
        if(record) {
            eshopOrderStore.remove(record);
            eshopOrderStore.sync({
                success: function(batch){
                    Ext.each(batch.operations, function (operation) {
                        var response = Ext.decode(operation.response.responseText);

                        // show new status
                        Ext.getCmp('eshop-eshopOrders-grid-statusbar').newStatus(response.message); 
                    });
                },
                failure: function(batch, options){
                    eshopOrderStore.rejectChanges();
                    Ext.each(batch.exceptions, function (exception) {
                        var response = exception.error || __jsa('Eshop', 'Action failed. Please try again.');

                        // show validation errors in grid cells
                        showErrorsInGrid(evtData.grid, evtData.rowIdx, response.errors);

                        // show new status
                        Ext.getCmp('eshop-eshopOrders-grid-statusbar').newStatus(response);
                    });
                }
            });
        }
    },

    afterEshopOrderEdit: function (editor, e) {
        e.store.sync({
            success: function(batch){
                Ext.each(batch.operations, function (operation) {
                    var response = Ext.decode(operation.response.responseText);
                    
                    // set data from response to record (i.e. field "modified")
                    Ext.each(response.data, function(key, value){
                        e.record.set(key, value);
                    });    
                    e.record.commit();
                    
                    // show new status
                    Ext.getCmp('eshop-eshopOrders-grid-statusbar').newStatus(response.message); 
                });
                // if we added new record
                if (e.originalValues.id == null) {
                    // remove silently(not visually) all in store
                    e.store.removeAll(true);
                    // load store from server
                    e.store.load();
                }
            },
            failure: function(batch, options){
                Ext.each(batch.exceptions, function (exception) {
                    var response = exception.error || __jsa('Eshop', 'Action failed. Please try again.');

                    // show validation errors in grid cells
                    showErrorsInGrid(e.grid, e.rowIdx, response.errors);
                    
                    // show new status
                    Ext.getCmp('eshop-eshopOrders-grid-statusbar').newStatus(response);
                });
            }
        });
    },
    
    onBeforeEdit: function (editor, e) {
        this.rowEditor.cancelEdit();
    },
    
    addEshopOrder: function () {
        var newEshopOrder,
            eshopOrderStore = this.getStore('Eshop.stores.EshopOrders');

        // check if first page is displayed
        var firstPage = false;
        Ext.Object.each(eshopOrderStore.data.map, function (key, value) {
            if (value.index == 0) {
                firstPage = true;
                return;
            }
        });

        // if we display not first page, start index must be 5, not 0
        var newIndex = 5;
        if (firstPage) newIndex = 0;
        
        // cancel not synchronized changes
        eshopOrderStore.rejectChanges();
        this.rowEditor.cancelEdit();
        
        // add new record to store
        newEshopOrder = eshopOrderStore.insert(newIndex, {
            id: null,
            run_users_id: null,
            status: '',
            payment_status: '',
            order_price_taxless: '',
            order_tax: '',
            order_price_actual_taxless: '',
            order_tax_actual: '',
            order_price_suggested_taxless: '',
            order_tax_suggested: '',
            products_price_taxless: '',
            products_tax: '',
            products_price_actual_taxless: '',
            products_tax_actual: '',
            products_price_suggested_taxless: '',
            products_tax_suggested: '',
            shipment_price_taxless: '',
            shipment_tax: '',
            shipment_price_actual_taxless: '',
            shipment_tax_actual: '',
            payment_price_taxless: '',
            payment_tax: '',
            payment_price_actual_taxless: '',
            payment_tax_actual: '',
            recycling_fee_taxless: '',
            recycling_fee_tax: '',
            run_eshop_shipment_methods_id: null,
            shipment_method_name: '',
            run_payment_methods_id: null,
            payment_method_name: '',
            delivery_fullname: '',
            delivery_street: '',
            delivery_city: '',
            delivery_country: '',
            delivery_zip: '',
            phone: '',
            email: '',
            created: '',
            modified: ''
        });
        // start row editor on this new record
        this.rowEditor.startEdit(newIndex, this.eshopOrdersGrid.columns[1]);
    },
    
    reloadEshopOrdersStore: function() {
        var eshopOrderStore = this.getStore('Eshop.stores.EshopOrders');
        eshopOrderStore.removeAll(true);
        eshopOrderStore.load();
    },
    
    removeFilter: function() {
        var eshopOrderStore = this.getStore('Eshop.stores.EshopOrders');
        var parameters = [{}];
        
        // clear inputs of runFilter        
        var filterInputs = Ext.query('#eshopeshopOrdersgrid .runFilter input');
        Ext.Array.each(filterInputs, function(dom, index) {
            var extDom = Ext.get(dom);
            if (extDom.getAttribute('type') == 'text') {
                extDom.set({
                    value: ''
                }, false);                
            }
            else if (extDom.getAttribute('type') == 'checkbox') {
                extDom.set({
                    checked: false
                }, false);                
            }
        });
        
        var filterSelects = Ext.query('#eshopeshopOrdersgrid .runFilter select');
        Ext.Array.each(filterSelects, function(dom, index) {
            var extDom = Ext.get(dom);
            extDom.down('option').set({
                selected: 'selected'
            }, false);                
        });
        
        Ext.apply(eshopOrderStore.getProxy().extraParams, {
            runFilter: Ext.encode(parameters)
        });
        eshopOrderStore.removeAll(true);
        eshopOrderStore.load();
    },
    
    onEshopOrderCancelEdit: function(editor, e, eOpts) {
        if (e.record.data.id == null) {
            e.store.remove(e.record);
        }
    },
    
    onEshopOrderGeneratePohodaXml: function(evtData) {
        var eshopOrderStore = this.getStore('Eshop.stores.EshopOrders');
        var record = eshopOrderStore.getAt(evtData.rowIndex);
        Ext.Ajax.request({
            url: '/mvc/Eshop/EshopOrders/admin_generatePohodaXmlOrder?lang=' + runConfig.lang,
            params: {
                orderId: record.data.id
            },
            success: function (response) {
                var jsonResponse = Ext.decode(response.responseText);
                Ext.getCmp('eshop-eshopOrders-grid-statusbar').newStatus(jsonResponse.message);
            },
            failure: function (batch) {
                Ext.each(batch.exceptions, function (exception) {
                    var response = exception.error || __jsa('Eshop', 'Action failed. Please try again.');
                    Ext.getCmp('eshop-eshopOrders-grid-statusbar').newStatus(response);
                });
            }
        });
    },

    onMallOrderUpdate: function(evtData) {
        var eshopOrderStore = this.getStore('Eshop.stores.EshopOrders');
        var record = eshopOrderStore.getAt(evtData.rowIndex);
        Ext.Ajax.request({
            url: '/mvc/Eshop/EshopOrders/admin_updateMallOrder',
            params: {
                orderId: record.data.id
            },
            success: function (response) {
                var jsonResponse = Ext.decode(response.responseText);
                Ext.getCmp('eshop-eshopOrders-grid-statusbar').newStatus(jsonResponse.message);
            },
            failure: function (batch) {
                Ext.each(batch.exceptions, function (exception) {
                    var response = exception.error || __jsa('Eshop', 'Action failed. Please try again.');
                    Ext.getCmp('eshop-eshopOrders-grid-statusbar').newStatus(response);
                });
            }
        });
    }
});

function _checkPayment(orderId, buttonElement) {
    var $button = jQuery(buttonElement), messageId = 'payment-message-' + orderId,
        style = 'font-weight:bold;';
    jQuery('#' + messageId).remove();
    jQuery.ajax({
        url: '/mvc/Eshop/EshopOrders/admin_checkTatrabankaPaymentStatus/' + orderId + '?lang=' + runConfig.lang,
        dataType: 'json',
        success: function (data) {
            if (data.paymentStatus === 'enum_payment_paid') {
                style += 'color:darkgreen;';
            }
            else if (!data.paymentStatus) {
                style += 'color:darkblue;';
            }
            else {
                style += 'color:darkred;';
            }
            $button.after('<div id="' + messageId + '" style="' + style + '">' + data.message + '</div>');
        },
        error: function() {
            style += 'color:darkred;';
            $button.after('<div id="' + messageId + '" style="' + style + '">Dopyt do banky zlyhal</div>');
        }
    });
}
